"use strict";(()=>{var e={};e.id=290,e.ids=[290],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4206:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>c,originalPathname:()=>l,requestAsyncStorage:()=>i,routeModule:()=>s,serverHooks:()=>p,staticGenerationAsyncStorage:()=>u,staticGenerationBailout:()=>d});var a={};r.r(a),r.d(a,{POST:()=>POST});var o=r(884),n=r(6132);async function POST(e){let t=await e.json();try{let e=await fetch("http://localhost:8080/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!e.ok)throw Error("Failed to generate resume");let r=await e.json();return Response.json(r)}catch(e){return console.error("Error:",e),Response.json({error:"Failed to generate resume"},{status:500})}}let s=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/generate/route",pathname:"/api/generate",filename:"route",bundlePath:"app/api/generate/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Resume custom/app/api/generate/route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:i,staticGenerationAsyncStorage:u,serverHooks:p,headerHooks:c,staticGenerationBailout:d}=s,l="/api/generate/route"}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[729],()=>__webpack_exec__(4206));module.exports=r})();