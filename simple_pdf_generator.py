"""
Professional PDF generator that matches <PERSON>'s original resume format exactly
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import black
from io import BytesIO
import textwrap
import re

def create_complete_resume_pdf(content: str) -> bytes:
    """
    Create a professional PDF that matches <PERSON>'s original resume format exactly
    """
    buffer = BytesIO()
    p = canvas.Canvas(buffer, pagesize=letter)
    width, height = letter

    # Professional margins and spacing
    left_margin = 72  # 1 inch
    right_margin = width - 72
    top_margin = height - 72
    y_position = top_margin

    # Parse content to match original structure
    formatted_content = parse_resume_content(content)

    # Render each section
    for section in formatted_content:
        y_position = render_section(p, section, left_margin, right_margin, y_position, height)

        # Check if we need a new page
        if y_position < 100:
            p.showPage()
            y_position = top_margin

    p.save()
    pdf_bytes = buffer.getvalue()
    buffer.close()
    return pdf_bytes

def parse_resume_content(content: str) -> list:
    """Parse resume content into structured sections matching original format"""
    sections = []
    lines = content.split('\n')
    current_section = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Name section
        if '<PERSON>' in line and not current_section:
            sections.append({
                'type': 'name',
                'content': '<PERSON>enne'
            })

        # Contact info
        elif 'Berlin, Germany' in line and '•' in line:
            sections.append({
                'type': 'contact',
                'content': line
            })

        # Professional title
        elif 'AI Sales Strategist' in line and '|' in line:
            sections.append({
                'type': 'title',
                'content': line
            })

        # Summary
        elif line.startswith('Product-led'):
            # Collect full summary
            summary_lines = [line]
            continue

        # Section headers
        elif line.isupper() and len(line) > 5:
            if current_section:
                sections.append(current_section)
            current_section = {
                'type': 'section',
                'header': line,
                'items': []
            }

        # Job titles and project titles
        elif any(title in line for title in ['Account Manager', 'AI Solutions Consultant', 'Digital Experience Designer', 'Project Lead', 'Design & Research', 'Ad Intelligence Tracker', 'AI Lead Gen Tool', 'Modular AI Content']):
            if current_section:
                current_section['items'].append({
                    'type': 'job_title',
                    'content': line
                })

        # Company/date info
        elif '•' in line and any(word in line for word in ['Gladtobe', 'Freelance', 'ReflectOn', 'Bewatec', 'University', 'France']):
            if current_section and current_section['items']:
                current_section['items'][-1]['company'] = line

        # Bullet points
        elif line.startswith('•'):
            if current_section:
                current_section['items'].append({
                    'type': 'bullet',
                    'content': line[1:].strip()
                })

        # Regular content
        else:
            if current_section:
                current_section['items'].append({
                    'type': 'text',
                    'content': line
                })

    # Add final section
    if current_section:
        sections.append(current_section)

    return sections

def render_section(p, section, left_margin, right_margin, y_position, page_height):
    """Render a section with professional formatting"""

    if section['type'] == 'name':
        # Name - large, bold, centered
        p.setFont("Helvetica-Bold", 20)
        text_width = p.stringWidth(section['content'], "Helvetica-Bold", 20)
        x_position = (right_margin + left_margin - text_width) / 2
        p.drawString(x_position, y_position, section['content'])
        return y_position - 25

    elif section['type'] == 'contact':
        # Contact info - centered, smaller
        p.setFont("Helvetica", 10)
        text_width = p.stringWidth(section['content'], "Helvetica", 10)
        x_position = (right_margin + left_margin - text_width) / 2
        p.drawString(x_position, y_position, section['content'])
        return y_position - 20

    elif section['type'] == 'title':
        # Professional title - centered, bold
        p.setFont("Helvetica-Bold", 12)
        text_width = p.stringWidth(section['content'], "Helvetica-Bold", 12)
        x_position = (right_margin + left_margin - text_width) / 2
        p.drawString(x_position, y_position, section['content'])
        return y_position - 25

    elif section['type'] == 'section':
        # Section header - bold, left-aligned
        y_position -= 10  # Extra space before section
        p.setFont("Helvetica-Bold", 12)
        p.drawString(left_margin, y_position, section['header'])
        y_position -= 18

        # Render section items
        for item in section['items']:
            y_position = render_item(p, item, left_margin, right_margin, y_position, page_height)

        return y_position

    return y_position

def render_item(p, item, left_margin, right_margin, y_position, page_height):
    """Render individual items within sections"""

    if item['type'] == 'job_title':
        # Job title - bold
        p.setFont("Helvetica-Bold", 11)
        p.drawString(left_margin, y_position, item['content'])
        y_position -= 14

        # Company info if available
        if 'company' in item:
            p.setFont("Helvetica", 10)
            p.drawString(left_margin, y_position, item['company'])
            y_position -= 16

    elif item['type'] == 'bullet':
        # Bullet point
        p.setFont("Helvetica", 10)
        bullet_text = f"• {item['content']}"

        # Wrap long text
        max_width = int((right_margin - left_margin - 20) / 6)  # Approximate chars per line
        if len(bullet_text) > max_width:
            wrapped = textwrap.wrap(bullet_text, width=max_width)
            for i, line in enumerate(wrapped):
                if i == 0:
                    p.drawString(left_margin, y_position, line)
                else:
                    p.drawString(left_margin + 12, y_position, line)
                y_position -= 12
        else:
            p.drawString(left_margin, y_position, bullet_text)
            y_position -= 12

    elif item['type'] == 'text':
        # Regular text
        p.setFont("Helvetica", 10)

        # Wrap long text
        max_width = int((right_margin - left_margin) / 6)
        if len(item['content']) > max_width:
            wrapped = textwrap.wrap(item['content'], width=max_width)
            for line in wrapped:
                p.drawString(left_margin, y_position, line)
                y_position -= 12
        else:
            p.drawString(left_margin, y_position, item['content'])
            y_position -= 12

    return y_position

def format_resume_for_pdf(content: str) -> str:
    """
    Pre-format the resume content to match Alex's original structure exactly
    """
    # Clean and structure the content to match original format
    lines = content.split('\n')
    formatted_lines = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue

        # Handle name and contact info
        if 'Alex Guyenne' in line:
            # Extract name cleanly
            if 'Berlin' in line:
                name_part = line.split('Berlin')[0].strip()
                contact_part = 'Berlin' + line.split('Berlin')[1]
                formatted_lines.append(name_part)
                formatted_lines.append(contact_part)
            else:
                formatted_lines.append(line)
            i += 1
            continue

        # Handle professional title
        if 'AI Sales Strategist' in line and '|' in line:
            formatted_lines.append(line)
            i += 1
            continue

        # Handle summary
        if line.startswith('Product-led'):
            # Collect full summary
            summary_parts = [line]
            i += 1
            while i < len(lines) and lines[i].strip() and not lines[i].strip().isupper():
                summary_parts.append(lines[i].strip())
                i += 1

            # Join summary into proper paragraphs
            full_summary = ' '.join(summary_parts)
            # Break into readable lines
            import textwrap
            wrapped_summary = textwrap.fill(full_summary, width=90)
            formatted_lines.append(wrapped_summary)
            continue

        # Handle section headers
        if line.isupper() and len(line) > 5:
            formatted_lines.append('')  # Add space before section
            formatted_lines.append(line)
            i += 1
            continue

        # Handle job entries
        if any(title in line for title in ['Account Manager', 'AI Solutions Consultant', 'Digital Experience Designer', 'Project Lead', 'Design & Research']):
            formatted_lines.append('')  # Space before job
            formatted_lines.append(line)

            # Look for company/date info on next lines
            i += 1
            while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('•'):
                company_line = lines[i].strip()
                if company_line:
                    formatted_lines.append(company_line)
                i += 1
            continue

        # Handle project titles
        if any(project in line for project in ['Ad Intelligence Tracker', 'AI Lead Gen Tool', 'Modular AI Content']):
            formatted_lines.append('')  # Space before project
            formatted_lines.append(line)
            i += 1
            continue

        # Handle education entries
        if "Master's" in line or "Bachelor's" in line:
            formatted_lines.append('')  # Space before education
            formatted_lines.append(line)

            # Look for university info
            i += 1
            while i < len(lines) and lines[i].strip() and '•' in lines[i]:
                formatted_lines.append(lines[i].strip())
                i += 1
            continue

        # Handle everything else
        formatted_lines.append(line)
        i += 1

    return '\n'.join(formatted_lines)
