<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/581bd207adb556b3.css" crossorigin="" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/039a721b25a963ad.css" crossorigin="" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-324ff3d6d6c6899f.js" crossorigin=""/><script src="/_next/static/chunks/fd9d1056-0e221b447477ba64.js" async="" crossorigin=""></script><script src="/_next/static/chunks/472-a90b3ac8a5fd9311.js" async="" crossorigin=""></script><script src="/_next/static/chunks/main-app-2d43fd33f690a331.js" async="" crossorigin=""></script><script src="/_next/static/chunks/393-00b556f0788972c5.js" async=""></script><script src="/_next/static/chunks/app/page-901292b9b5925513.js" async=""></script><title>Resume Tailor - AI-Powered Resume Optimization</title><meta name="description" content="Optimize your resume for specific job applications using AI"/><script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js" crossorigin="" noModule=""></script></head><body><main class="page_main__nw1Wk"><h1 class="page_title__po7na">Resume Tailor</h1><p class="page_description__lvaOp">Optimize your resume for specific job applications</p><form class="page_form__NxPAx"><div class="page_inputGroup__keP_G"><label for="jobDescription">Job Description *</label><textarea id="jobDescription" placeholder="Paste the full job description here..." required="" rows="8"></textarea></div><div class="page_inputGroup__keP_G"><label for="resumeContent">Your Current Resume *</label><div class="page_fileUploadSection__h651M"><textarea id="resumeContent" placeholder="Paste your current resume content here..." required="" rows="8"></textarea><div class="page_fileUploadOption__lJq5z"><span>Or upload a file:</span><input type="file" accept=".pdf,.txt" class="page_fileInput__3Qiq7"/><span class="page_fileHint__6NIeM">Supports PDF and text files (max 10MB)</span></div></div></div><div class="page_inputGroup__keP_G"><label for="keyAchievements">Key Achievements (Optional)</label><textarea id="keyAchievements" placeholder="List 1-3 key achievements you want to highlight for this specific job..." rows="4"></textarea></div><div class="page_inputGroup__keP_G"><label for="specificConcerns">Specific Concerns (Optional)</label><textarea id="specificConcerns" placeholder="Any specific parts of your resume you want to improve or aspects of the job you want to emphasize..." rows="3"></textarea></div><button type="submit" class="page_button__52WaL">Generate Tailored Resume</button></form></main><script src="/_next/static/chunks/webpack-324ff3d6d6c6899f.js" crossorigin="" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/581bd207adb556b3.css\",\"style\",{\"crossOrigin\":\"\"}]\n0:\"$L2\"\n"])</script><script>self.__next_f.push([1,"3:HL[\"/_next/static/css/039a721b25a963ad.css\",\"style\",{\"crossOrigin\":\"\"}]\n"])</script><script>self.__next_f.push([1,"4:I[3728,[],\"\"]\n6:I[9928,[],\"\"]\n7:I[6954,[],\"\"]\n8:I[7264,[],\"\"]\na:I[8297,[],\"\"]\nb:I[3567,[\"393\",\"static/chunks/393-00b556f0788972c5.js\",\"931\",\"static/chunks/app/page-901292b9b5925513.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/581bd207adb556b3.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]],[\"$\",\"$L4\",null,{\"buildId\":\"Y32ofTtgXnE4_YRMxItGW\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/\",\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialHead\":[false,\"$L5\"],\"globalErrorComponent\":\"$6\",\"children\":[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],\"notFoundStyles\":[],\"childProp\":{\"current\":[\"$L9\",[\"$\",\"$La\",null,{\"propsForComponent\":{\"params\":{}},\"Component\":\"$b\",\"isStaticGeneration\":true}],null],\"segment\":\"__PAGE__\"},\"styles\":[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/039a721b25a963ad.css\",\"precedence\":\"next\",\"crossOrigin\":\"\"}]]}]}]}],null]}]]\n"])</script><script>self.__next_f.push([1,"5:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"Resume Tailor - AI-Powered Resume Optimization\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Optimize your resume for specific job applications using AI\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,""])</script></body></html>