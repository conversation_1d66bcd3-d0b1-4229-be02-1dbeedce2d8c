"""
PDF Generator for Resume Export
Converts markdown resume to professional PDF format
"""

import re
from io import BytesIO
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, KeepTogether
from reportlab.lib.enums import TA_LEFT, TA_CENTER
from reportlab.lib.colors import black, darkblue

def generate_pdf_from_markdown(markdown_content: str) -> bytes:
    """
    Generate a professional PDF from markdown resume content
    """
    try:
        buffer = BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=0.75*inch,
            leftMargin=0.75*inch,
            topMargin=0.75*inch,
            bottomMargin=0.75*inch
        )

        # Create custom styles
        styles = create_custom_styles()

        # Clean and parse markdown
        cleaned_content = clean_markdown_for_pdf(markdown_content)
        story = parse_markdown_to_story(cleaned_content, styles)

        # Add some content if story is empty
        if not story:
            story = [Paragraph("Resume content could not be parsed", styles['Normal'])]

        # Build PDF
        doc.build(story)

        # Get PDF bytes
        pdf_bytes = buffer.getvalue()
        buffer.close()

        return pdf_bytes

    except Exception as e:
        # Fallback: create a simple PDF with error message
        print(f"PDF generation error: {e}")
        return create_fallback_pdf(markdown_content)

def create_custom_styles():
    """Create custom styles for the resume PDF"""
    styles = getSampleStyleSheet()
    
    # Name style
    styles.add(ParagraphStyle(
        name='Name',
        parent=styles['Title'],
        fontSize=20,
        spaceAfter=6,
        alignment=TA_CENTER,
        textColor=darkblue
    ))
    
    # Contact style
    styles.add(ParagraphStyle(
        name='Contact',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=12,
        alignment=TA_CENTER,
        textColor=black
    ))
    
    # Professional title style
    styles.add(ParagraphStyle(
        name='ProfTitle',
        parent=styles['Normal'],
        fontSize=14,
        spaceAfter=8,
        alignment=TA_CENTER,
        textColor=darkblue,
        fontName='Helvetica-Bold'
    ))
    
    # Section header style
    styles.add(ParagraphStyle(
        name='SectionHeader',
        parent=styles['Heading2'],
        fontSize=12,
        spaceAfter=6,
        spaceBefore=12,
        textColor=darkblue,
        fontName='Helvetica-Bold'
    ))
    
    # Job title style
    styles.add(ParagraphStyle(
        name='JobTitle',
        parent=styles['Normal'],
        fontSize=11,
        spaceAfter=3,
        fontName='Helvetica-Bold'
    ))
    
    # Bullet point style
    styles.add(ParagraphStyle(
        name='Bullet',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=3,
        leftIndent=20,
        bulletIndent=10
    ))
    
    return styles

def parse_markdown_to_story(markdown_content: str, styles):
    """Parse markdown content and convert to reportlab story"""
    story = []
    lines = markdown_content.split('\n')
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        # Name (first # header)
        if line.startswith('# '):
            name = line[2:].strip()
            story.append(Paragraph(name, styles['Name']))
            
        # Contact information (line after name)
        elif 'Berlin, Germany' in line and '•' in line:
            story.append(Paragraph(line, styles['Contact']))
            
        # Professional title (bold text)
        elif line.startswith('**') and line.endswith('**') and 'AI' in line:
            title = line[2:-2]  # Remove ** markers
            story.append(Paragraph(title, styles['ProfTitle']))
            
        # Section headers (## or all caps)
        elif line.startswith('## ') or (line.isupper() and len(line) > 3):
            header = line.replace('## ', '').strip()
            story.append(Paragraph(header, styles['SectionHeader']))
            
        # Job titles (bold text with |)
        elif line.startswith('**') and '|' in line:
            job_info = line[2:-2] if line.endswith('**') else line[2:]  # Remove ** markers
            story.append(Paragraph(job_info, styles['JobTitle']))
            
        # Bullet points
        elif line.startswith('• '):
            bullet_text = line[2:].strip()
            story.append(Paragraph(f"• {bullet_text}", styles['Bullet']))
            
        # Regular paragraphs (summary, etc.)
        elif len(line) > 20 and not line.startswith(('**', '#', '•')):
            story.append(Paragraph(line, styles['Normal']))
            story.append(Spacer(1, 6))
        
        i += 1
    
    return story

def clean_markdown_for_pdf(markdown_content: str) -> str:
    """Clean markdown content for better PDF rendering"""
    
    # Remove extra markdown formatting that doesn't render well in PDF
    content = re.sub(r'\*\*([^*]+)\*\*', r'<b>\1</b>', markdown_content)  # Bold
    content = re.sub(r'\*([^*]+)\*', r'<i>\1</i>', content)  # Italic
    
    # Clean up spacing
    content = re.sub(r'\n{3,}', '\n\n', content)
    
    return content

def create_fallback_pdf(content: str) -> bytes:
    """Create a simple fallback PDF when main generation fails"""
    try:
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()

        story = []
        story.append(Paragraph("Alex Guyenne - Resume", styles['Title']))
        story.append(Spacer(1, 12))

        # Split content into paragraphs and add to story
        paragraphs = content.split('\n\n')
        for para in paragraphs[:10]:  # Limit to first 10 paragraphs
            if para.strip():
                clean_para = para.replace('#', '').replace('**', '').strip()
                if len(clean_para) > 10:
                    story.append(Paragraph(clean_para, styles['Normal']))
                    story.append(Spacer(1, 6))

        doc.build(story)
        pdf_bytes = buffer.getvalue()
        buffer.close()
        return pdf_bytes

    except Exception as e:
        print(f"Fallback PDF creation failed: {e}")
        # Return minimal PDF
        buffer = BytesIO()
        buffer.write(b'%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] >>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<< /Size 4 /Root 1 0 R >>\nstartxref\n174\n%%EOF')
        return buffer.getvalue()
