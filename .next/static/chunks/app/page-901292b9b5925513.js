(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{2260:function(e,a,r){Promise.resolve().then(r.bind(r,3567))},3567:function(e,a,r){"use strict";r.r(a),r.d(a,{default:function(){return Home}});var t=r(7437),o=r(2265),i=r(7393),s=r(328),n=r.n(s);function Home(){let[e,a]=(0,o.useState)(""),[r,s]=(0,o.useState)(""),[l,c]=(0,o.useState)(""),[d,p]=(0,o.useState)(""),[u,h]=(0,o.useState)(null),[m,_]=(0,o.useState)(!1),[f,w]=(0,o.useState)(null),[x,j]=(0,o.useState)(!1),[g,v]=(0,o.useState)(""),handleSubmit=async a=>{a.preventDefault(),_(!0),w(null),h(null);try{let a=await fetch("/api/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({job_description:e,base_resume_content:r,key_achievements:l,specific_concerns:d})});if(!a.ok)throw Error("HTTP error! status: ".concat(a.status));let t=await a.json();if(t.error)throw Error(t.error);h(t)}catch(e){console.error("Error:",e),w(e.message||"An error occurred while generating the resume")}finally{_(!1)}},handleCopyToClipboard=async()=>{if(null==u?void 0:u.tailored_resume_markdown)try{await navigator.clipboard.writeText(u.tailored_resume_markdown),j(!0),setTimeout(()=>j(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}},handleFileUpload=async e=>{let a=e.target.files[0];if(a){w(null),v("Processing file...");try{if(a.size>10485760)throw Error("File size too large. Please upload a file smaller than 10MB.");let e=a.name.toLowerCase();if(!e.endsWith(".pdf")&&!e.endsWith(".txt"))throw Error("Unsupported file type. Please upload a PDF or text file.");v("Uploading and processing file...");let r=new FormData;r.append("file",a);let t=await fetch("/api/upload",{method:"POST",body:r});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to process file")}let o=await t.json();if(!o.text||!o.text.trim())throw Error("No text content found in the file. Please check your file and try again.");s(o.text),v("File processed successfully!"),setTimeout(()=>v(""),2e3)}catch(e){console.error("File upload error:",e),w(e.message),v("")}}};return(0,t.jsxs)("main",{className:n().main,children:[(0,t.jsx)("h1",{className:n().title,children:"Resume Tailor"}),(0,t.jsx)("p",{className:n().description,children:"Optimize your resume for specific job applications"}),(0,t.jsxs)("form",{onSubmit:handleSubmit,className:n().form,children:[(0,t.jsxs)("div",{className:n().inputGroup,children:[(0,t.jsx)("label",{htmlFor:"jobDescription",children:"Job Description *"}),(0,t.jsx)("textarea",{id:"jobDescription",value:e,onChange:e=>a(e.target.value),placeholder:"Paste the full job description here...",required:!0,rows:8})]}),(0,t.jsxs)("div",{className:n().inputGroup,children:[(0,t.jsx)("label",{htmlFor:"resumeContent",children:"Your Current Resume *"}),(0,t.jsxs)("div",{className:n().fileUploadSection,children:[(0,t.jsx)("textarea",{id:"resumeContent",value:r,onChange:e=>s(e.target.value),placeholder:"Paste your current resume content here...",required:!0,rows:8}),(0,t.jsxs)("div",{className:n().fileUploadOption,children:[(0,t.jsx)("span",{children:"Or upload a file:"}),(0,t.jsx)("input",{type:"file",accept:".pdf,.txt",onChange:handleFileUpload,className:n().fileInput}),(0,t.jsx)("span",{className:n().fileHint,children:"Supports PDF and text files (max 10MB)"})]}),g&&(0,t.jsx)("div",{className:n().uploadProgress,children:g})]})]}),(0,t.jsxs)("div",{className:n().inputGroup,children:[(0,t.jsx)("label",{htmlFor:"keyAchievements",children:"Key Achievements (Optional)"}),(0,t.jsx)("textarea",{id:"keyAchievements",value:l,onChange:e=>c(e.target.value),placeholder:"List 1-3 key achievements you want to highlight for this specific job...",rows:4})]}),(0,t.jsxs)("div",{className:n().inputGroup,children:[(0,t.jsx)("label",{htmlFor:"specificConcerns",children:"Specific Concerns (Optional)"}),(0,t.jsx)("textarea",{id:"specificConcerns",value:d,onChange:e=>p(e.target.value),placeholder:"Any specific parts of your resume you want to improve or aspects of the job you want to emphasize...",rows:3})]}),(0,t.jsx)("button",{type:"submit",className:n().button,disabled:m,children:m?"Generating Tailored Resume...":"Generate Tailored Resume"})]}),f&&(0,t.jsxs)("div",{className:n().error,children:[(0,t.jsx)("h3",{children:"Error"}),(0,t.jsx)("p",{children:f})]}),u&&(0,t.jsxs)("div",{className:n().result,children:[(0,t.jsx)("h2",{children:"Your Tailored Resume"}),(0,t.jsxs)("div",{className:n().scoreCard,children:[(0,t.jsxs)("h3",{children:["Match Score: ",u.match_score,"/100"]}),(0,t.jsx)("div",{className:n().rationale,children:(0,t.jsx)(i.UG,{children:u.match_rationale})})]}),u.strategic_rationale&&(0,t.jsxs)("div",{className:n().strategicRationale,children:[(0,t.jsx)("h3",{children:"Strategic Rationale"}),(0,t.jsx)(i.UG,{children:u.strategic_rationale})]}),(0,t.jsxs)("div",{className:n().resumePreview,children:[(0,t.jsxs)("div",{className:n().previewHeader,children:[(0,t.jsx)("h3",{children:"Resume Preview"}),(0,t.jsxs)("div",{className:n().actionButtons,children:[(0,t.jsx)("button",{className:n().copyButton,onClick:handleCopyToClipboard,title:"Copy to clipboard",children:x?"✓ Copied!":"\uD83D\uDCCB Copy"}),(0,t.jsx)("button",{className:n().downloadButton,onClick:()=>{let e=new Blob([u.tailored_resume_markdown],{type:"text/markdown"}),a=URL.createObjectURL(e),r=document.createElement("a");r.href=a,r.download="tailored_resume.md",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a)},title:"Download as Markdown",children:"\uD83D\uDCE5 Download MD"})]})]}),(0,t.jsx)("div",{className:n().markdownPreview,children:(0,t.jsx)(i.UG,{children:u.tailored_resume_markdown})}),(0,t.jsxs)("div",{className:n().rawMarkdown,children:[(0,t.jsx)("h4",{children:"Raw Markdown (for copying)"}),(0,t.jsx)("pre",{className:n().markdownRaw,children:u.tailored_resume_markdown})]})]})]})]})}},328:function(e){e.exports={main:"page_main__nw1Wk",title:"page_title__po7na",description:"page_description__lvaOp",form:"page_form__NxPAx",inputGroup:"page_inputGroup__keP_G",fileUploadSection:"page_fileUploadSection__h651M",fileUploadOption:"page_fileUploadOption__lJq5z",fileInput:"page_fileInput__3Qiq7",fileHint:"page_fileHint__6NIeM",uploadProgress:"page_uploadProgress__1s8yN",button:"page_button__52WaL",error:"page_error__JBrsB",result:"page_result__7WhcO",scoreCard:"page_scoreCard__pEla_",rationale:"page_rationale__E_Uco",strategicRationale:"page_strategicRationale__zwoxY",previewHeader:"page_previewHeader__nnSzV",actionButtons:"page_actionButtons__aac_D",copyButton:"page_copyButton__UNIa2",downloadButton:"page_downloadButton__lvXC9",markdownPreview:"page_markdownPreview__w7jd6",rawMarkdown:"page_rawMarkdown__xjVcb",markdownRaw:"page_markdownRaw__IVI0h"}}},function(e){e.O(0,[393,971,472,744],function(){return e(e.s=2260)}),_N_E=e.O()}]);