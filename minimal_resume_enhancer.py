"""
Minimal Resume Enhancement - Preserves Authenticity
This module provides conservative resume enhancement that maintains the original voice and structure.
"""

import re
from llm_service import llm_service

def enhance_resume_minimal(job_description: str, base_resume_content: str, key_achievements: list = None) -> dict:
    """
    Minimal resume enhancement that preserves authenticity and ALL content.
    Priority: Complete content preservation over formatting.
    """
    if key_achievements is None:
        key_achievements = []

    # PRIORITY 1: Ensure ALL content is preserved
    enhanced_content = ensure_all_content_preserved(base_resume_content)

    # PRIORITY 2: Basic formatting improvements only
    enhanced_content = apply_minimal_formatting(enhanced_content)

    # Extract a few key terms from job description for strategic placement
    job_keywords = extract_key_terms(job_description)

    # Only enhance if LLM is available and we can make subtle improvements
    if llm_service.is_enabled():
        # Try to enhance the professional summary only
        summary_match = re.search(r'(Product-led.*?adoption[^.]*\.)', enhanced_content, re.DOTALL)
        if summary_match:
            original_summary = summary_match.group(1)
            enhanced_summary = llm_service.enhance_summary(original_summary, job_description, key_achievements)

            # Only replace if the enhancement is subtle and maintains authenticity
            if enhanced_summary and enhanced_summary != original_summary:
                # Check if the enhancement is too different (indicates over-enhancement)
                if calculate_similarity(original_summary, enhanced_summary) > 0.7:
                    enhanced_content = enhanced_content.replace(original_summary, enhanced_summary)

    # Calculate match score
    match_score = calculate_basic_match_score(job_description, enhanced_content)

    # Generate simple insights
    insights = generate_simple_insights(job_description, enhanced_content, job_keywords)

    return {
        'tailored_resume_markdown': enhanced_content,
        'match_score': match_score,
        'match_rationale': insights,
        'strategic_rationale': 'Complete content preservation with minimal formatting improvements.'
    }

def extract_key_terms(job_description: str, max_terms: int = 5) -> list:
    """Extract key terms from job description without over-processing"""
    # Simple keyword extraction focusing on important terms
    text = job_description.lower()
    
    # Look for important terms
    important_terms = []
    
    # Technical terms
    tech_terms = ['ai', 'automation', 'digital', 'data', 'analytics', 'cloud', 'api', 'python', 'sql']
    for term in tech_terms:
        if term in text:
            important_terms.append(term)
    
    # Business terms  
    business_terms = ['strategy', 'sales', 'account', 'client', 'customer', 'growth', 'revenue', 'management']
    for term in business_terms:
        if term in text:
            important_terms.append(term)
    
    # Role-specific terms
    role_terms = ['consultant', 'manager', 'strategist', 'analyst', 'specialist', 'lead']
    for term in role_terms:
        if term in text:
            important_terms.append(term)
    
    return important_terms[:max_terms]

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate simple similarity between two texts"""
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def calculate_basic_match_score(job_description: str, resume_content: str) -> int:
    """Calculate an improved match score based on weighted keyword analysis"""

    # Extract important keywords with weights
    job_keywords = extract_weighted_keywords(job_description)
    resume_keywords = extract_weighted_keywords(resume_content)

    if not job_keywords:
        return 50

    total_weight = 0
    matched_weight = 0

    for keyword, weight in job_keywords.items():
        total_weight += weight
        if keyword in resume_keywords:
            matched_weight += weight

    if total_weight == 0:
        return 50

    # Calculate base score
    base_score = int((matched_weight / total_weight) * 100)

    # Bonus points for relevant experience
    bonus = 0
    if 'ai' in resume_content.lower() and 'ai' in job_description.lower():
        bonus += 10
    if 'automation' in resume_content.lower() and 'automation' in job_description.lower():
        bonus += 10
    if 'sales' in resume_content.lower() and 'sales' in job_description.lower():
        bonus += 5
    if 'account' in resume_content.lower() and 'account' in job_description.lower():
        bonus += 5

    final_score = min(base_score + bonus, 95)
    return max(final_score, 45)  # Minimum reasonable score

def extract_weighted_keywords(text: str) -> dict:
    """Extract keywords with importance weights"""
    text_lower = text.lower()
    keywords = {}

    # High-value keywords (weight 3)
    high_value = ['ai', 'artificial intelligence', 'automation', 'machine learning', 'sales', 'account manager', 'consultant']
    for keyword in high_value:
        if keyword in text_lower:
            keywords[keyword] = 3

    # Medium-value keywords (weight 2)
    medium_value = ['strategy', 'digital', 'innovation', 'client', 'customer', 'business development', 'saas', 'b2b']
    for keyword in medium_value:
        if keyword in text_lower:
            keywords[keyword] = 2

    # Standard keywords (weight 1)
    standard = ['experience', 'management', 'development', 'design', 'analysis', 'collaboration', 'leadership']
    for keyword in standard:
        if keyword in text_lower:
            keywords[keyword] = 1

    return keywords

def generate_simple_insights(job_description: str, resume_content: str, keywords: list) -> str:
    """Generate simple, actionable insights"""
    match_score = calculate_basic_match_score(job_description, resume_content)
    
    insights = f"""Match Score: {match_score}/100

Key Observations:
• Your resume shows good alignment with the role requirements
• Your experience in AI and automation is highly relevant
• Consider naturally incorporating these terms where appropriate: {', '.join(keywords[:3])}

Strengths:
• Strong technical background in AI and automation tools
• Proven track record in client-facing roles
• Experience with cross-functional collaboration

Recommendations:
• Your authentic experience speaks for itself
• Focus on quantifiable achievements where possible
• Ensure your unique voice and personality come through

Note: This analysis preserves your authentic voice while identifying strategic alignment opportunities."""
    
    return insights

def fix_resume_formatting(content: str) -> str:
    """Fix common formatting issues while preserving original structure"""

    # Start with a clean approach - preserve the original structure better
    lines = content.split('\n')
    formatted_lines = []

    # Process line by line to maintain structure
    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # Skip empty lines but preserve spacing
        if not line:
            formatted_lines.append('')
            i += 1
            continue

        # Handle name and contact info
        if 'Alex Guyenne' in line and 'Berlin' in line:
            # Split name and contact info properly
            if 'Berlin, Germany' in line:
                # Extract name
                name_part = line.split('Berlin')[0].strip()
                contact_part = 'Berlin' + line.split('Berlin')[1]

                formatted_lines.append(f"# {name_part}")
                formatted_lines.append(contact_part)
                formatted_lines.append('')
            else:
                formatted_lines.append(f"# {line}")
            i += 1
            continue

        # Handle professional title
        if 'AI Sales Strategist' in line and '|' in line:
            formatted_lines.append(f"**{line}**")
            formatted_lines.append('')
            i += 1
            continue

        # Handle summary (starts with Product-led)
        if line.startswith('Product-led'):
            # Collect the full summary
            summary_lines = [line]
            i += 1
            while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('WORK'):
                summary_lines.append(lines[i].strip())
                i += 1

            # Join and format summary
            full_summary = ' '.join(summary_lines)
            formatted_lines.append(full_summary)
            formatted_lines.append('')
            continue

        # Handle section headers
        if line.isupper() and len(line) > 5:
            formatted_lines.append(f"## {line}")
            formatted_lines.append('')
            i += 1
            continue

        # Handle job titles (look for patterns)
        if any(title in line for title in ['Account Manager', 'AI Solutions Consultant', 'Digital Experience Designer', 'Project Lead', 'Design & Research']):
            # Format as job title
            formatted_lines.append(f"**{line}**")
            formatted_lines.append('')
            i += 1
            continue

        # Handle bullet points
        if line.startswith('•'):
            formatted_lines.append(line)
            i += 1
            continue

        # Handle everything else
        formatted_lines.append(line)
        i += 1

    # Join back together
    result = '\n'.join(formatted_lines)

    # Clean up excessive line breaks
    result = re.sub(r'\n{3,}', '\n\n', result)

    return result

def fix_contact_formatting(content: str) -> str:
    """Fix contact information formatting to professional standard"""

    # Extract name - look for "Alex Guyenne" specifically
    name_match = re.search(r'Alex Guyenne', content)
    name = name_match.group(0) if name_match else "Alex Guyenne"

    # Extract contact details
    phone_match = re.search(r'\+\d+', content)
    email_match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', content)
    linkedin_match = re.search(r'linkedin\.com/in/[a-zA-Z0-9-]+', content)

    phone = phone_match.group(0) if phone_match else "+4915774654068"
    email = email_match.group(0) if email_match else "<EMAIL>"
    linkedin = linkedin_match.group(0) if linkedin_match else "linkedin.com/in/alexguyenne"

    # Create properly formatted header
    formatted_header = f"""# {name}
Berlin, Germany • {phone} • {email} • {linkedin}

"""

    # Find where the professional title starts and keep everything from there
    title_patterns = [
        r'AI Sales Strategist',
        r'AI [^P]+(?:Enablement|Development|Solutions)',
        r'\*\*AI [^*]+\*\*'
    ]

    for pattern in title_patterns:
        title_match = re.search(pattern, content)
        if title_match:
            title_start = content.find(title_match.group(0))
            remaining_content = content[title_start:]
            return formatted_header + remaining_content

    # If no title found, just append everything after the first few lines
    lines = content.split('\n')
    # Skip lines that contain contact info
    start_idx = 0
    for i, line in enumerate(lines):
        if not any(contact in line for contact in ['+49', '@', 'linkedin', 'Berlin']) and len(line.strip()) > 10:
            start_idx = i
            break

    remaining_content = '\n'.join(lines[start_idx:])
    return formatted_header + remaining_content

def fix_title_and_summary_structure(content: str) -> str:
    """Fix professional title and summary structure to match original format"""

    # Extract the professional title - look for the pattern after contact info
    title_match = re.search(r'linkedin\.com/in/alexguyenne\s*([^P]+?)Product-led', content, re.DOTALL)
    if title_match:
        title = title_match.group(1).strip()
        # Clean up title
        title = re.sub(r'\s+', ' ', title)
        title = title.replace('**', '').strip()
    else:
        title = "AI Sales Strategist | Product-Led Builder | Automation Enablement"

    # Find the summary text (starts with "Product-led")
    summary_match = re.search(r'(Product-led[^W]+?)(?=WORK EXPERIENCE|$)', content, re.DOTALL)
    if summary_match:
        summary_text = summary_match.group(1).strip()
        # Clean up summary
        summary_text = re.sub(r'\s+', ' ', summary_text)
        # Ensure it ends with a period
        if not summary_text.endswith('.'):
            summary_text += '.'

        # Find where to insert the formatted section
        contact_end = content.find('linkedin.com/in/alexguyenne')
        if contact_end > 0:
            contact_end = content.find('\n', contact_end)
            if contact_end == -1:
                contact_end = len('linkedin.com/in/alexguyenne') + content.find('linkedin.com/in/alexguyenne')

            # Find start of work experience
            work_start = content.find('WORK EXPERIENCE')
            if work_start > 0:
                # Create properly formatted section with proper spacing
                formatted_section = f"""

**{title}**

{summary_text}

"""

                # Reconstruct content
                before = content[:contact_end]
                after = content[work_start:]

                return before + formatted_section + after

    return content

def fix_education_formatting(content: str) -> str:
    """Fix education section formatting issues"""
    # Fix broken "Master's" and "Bachelor's"
    content = re.sub(r"Master'\s*s\s+in", "Master's in", content)
    content = re.sub(r"Bachelor'\s*s\s+in", "Bachelor's in", content)

    # Fix university formatting
    content = re.sub(r'(Master\'s in [^•\n]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)
    content = re.sub(r'(Bachelor\'s in [^•\n]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)

    return content

def fix_work_experience_formatting(content: str) -> str:
    """Fix work experience formatting with proper line breaks"""

    # Add proper line breaks between job entries
    # Look for job title patterns and ensure they start on new lines

    # Fix Account Manager entry
    content = re.sub(r'Account Manager \|\s*AI automation\s*Gladtobe\s*•\s*Stuttgart/Berlin\s+(\d{2}/\d{4})\s*-\s*(Present|\d{2}/\d{4})',
                     r'**Account Manager - AI Automation** | Gladtobe, Stuttgart/Berlin | \1 - \2', content)

    # Fix AI Solutions Consultant entry - add line break before it
    content = re.sub(r'frameworks\s*(AI Solutions Consultant)',
                     r'frameworks\n\n**\1** | Freelance/Consulting | 04/2024 - Present', content)

    # Fix other job entries with proper line breaks
    content = re.sub(r'(Digital Experience Designer)\s*([A-Z][a-zA-Z\s]+)\s*,\s*([A-Za-z\s]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'\n\n**\1** | \2, \3 | \4 - \5', content)

    content = re.sub(r'(Project Lead Strategy and Innovation)\s*([A-Z][a-zA-Z\s&]+)\s*,\s*([A-Za-z\s]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'\n\n**\1** | \2, \3 | \4 - \5', content)

    content = re.sub(r'(Design & Research Innovation Lead)\s*([A-Z][a-zA-Z\s&]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'\n\n**\1** | \2 | \3 - \4', content)

    # Ensure bullet points are properly formatted with line breaks
    content = re.sub(r'•\s*([^•]+?)(?=•|\n\n|\*\*|[A-Z][a-z]+ [A-Z])', r'• \1\n', content)

    # Fix specific patterns from the original resume
    content = re.sub(r'phases\s*(Digital Experience Designer)',
                     r'phases\n\n**\1** | ReflectOn, Berlin | 07/2023 - 02/2024', content)

    content = re.sub(r'teams\s*(Design & Research Innovation Lead)',
                     r'teams\n\n**\1** | Bewatec Connected Care GmbH | 07/2019 - 12/2021', content)

    return content

def fix_project_highlights_formatting(content: str) -> str:
    """Fix project highlights formatting"""
    # Add proper line breaks between projects
    content = re.sub(r'(Ad Intelligence Tracker[^A-Z]+)(AI Lead Gen Tool)', r'\1\n\n**\2', content)
    content = re.sub(r'(AI Lead Gen Tool[^A-Z]+)(Modular AI Content Generator)', r'\1\n\n**\2', content)

    # Format project titles
    content = re.sub(r'Ad Intelligence Tracker', r'**Ad Intelligence Tracker**', content)
    content = re.sub(r'AI Lead Gen Tool for B2B Niche Validation', r'**AI Lead Gen Tool for B2B Niche Validation**', content)
    content = re.sub(r'Modular AI Content Generator for Telecom', r'**Modular AI Content Generator for Telecom**', content)

    return content

def clean_general_formatting(content: str) -> str:
    """Clean up general formatting issues"""
    # Fix multiple spaces
    content = re.sub(r' +', ' ', content)

    # Fix line breaks
    content = re.sub(r'\n{3,}', '\n\n', content)

    # Fix bullet points
    content = re.sub(r'•\s*([^•\n]+)', r'• \1', content)

    return content.strip()

def preserve_all_sections(content: str) -> str:
    """Ensure all resume sections are preserved and properly formatted"""

    # Add proper section headers if missing
    sections_to_check = [
        ('PROJECT HIGHLIGHT', 'PROJECT HIGHLIGHTS'),
        ('SKILLS & TOOLS', 'SKILLS & TOOLS'),
        ('EDUCATION', 'EDUCATION'),
        ('AWARDS', 'AWARDS'),
        ('Languages:', 'LANGUAGES')
    ]

    for old_header, new_header in sections_to_check:
        if old_header in content and new_header not in content:
            content = content.replace(old_header, f'\n\n## {new_header}\n')

    # Ensure proper spacing between sections
    content = re.sub(r'(WORK EXPERIENCE.*?)(\*\*Ad Intelligence)', r'\1\n\n## PROJECT HIGHLIGHTS\n\2', content, flags=re.DOTALL)
    content = re.sub(r'(campaign needs.*?)(GPT, Claude)', r'\1\n\n## SKILLS & TOOLS\n\2', content, flags=re.DOTALL)
    content = re.sub(r'(Mixpanel.*?)(English)', r'\1\n\n## LANGUAGES\n\2', content, flags=re.DOTALL)
    content = re.sub(r'(proficiency\).*?)(\*\*Master)', r'\1\n\n## EDUCATION\n\2', content, flags=re.DOTALL)
    content = re.sub(r'(12/2010.*?)(Winner)', r'\1\n\n## AWARDS\n\2', content, flags=re.DOTALL)

    return content

def optimize_job_titles(content: str, job_description: str) -> str:
    """Optimize job titles for better ATS matching"""

    # Extract key role terms from job description
    role_keywords = extract_role_keywords(job_description)

    # Current professional title optimization
    current_title = "AI Sales Strategist | Product-Led Builder | Automation Enablement"

    # Create ATS-optimized version based on job description
    if any(keyword in job_description.lower() for keyword in ['sales', 'account', 'business development']):
        optimized_title = "AI Sales Strategist | Account Management | Business Development"
    elif any(keyword in job_description.lower() for keyword in ['consultant', 'consulting', 'advisory']):
        optimized_title = "AI Solutions Consultant | Digital Strategy | Automation Expert"
    elif any(keyword in job_description.lower() for keyword in ['product', 'manager', 'product manager']):
        optimized_title = "AI Product Strategist | Digital Innovation | Automation Solutions"
    elif any(keyword in job_description.lower() for keyword in ['engineer', 'technical', 'developer']):
        optimized_title = "AI Solutions Engineer | Technical Strategy | Automation Development"
    else:
        # Keep original but make it more ATS-friendly
        optimized_title = "AI Strategist | Digital Innovation | Automation Solutions"

    # Replace the title if it's significantly different and relevant
    if optimized_title != current_title:
        content = content.replace(current_title, optimized_title)

    return content

def extract_role_keywords(job_description: str) -> list:
    """Extract role-specific keywords from job description"""
    role_terms = []
    text = job_description.lower()

    # Common role keywords
    role_keywords = [
        'sales', 'account manager', 'business development', 'consultant', 'consulting',
        'product manager', 'product', 'strategy', 'strategist', 'engineer', 'technical',
        'developer', 'analyst', 'specialist', 'lead', 'director', 'manager'
    ]

    for keyword in role_keywords:
        if keyword in text:
            role_terms.append(keyword)

    return role_terms

def ensure_complete_content(formatted_content: str, original_content: str) -> str:
    """Ensure all sections from original content are preserved"""

    # Key sections that must be preserved
    required_sections = [
        'PROJECT HIGHLIGHT',
        'SKILLS & TOOLS',
        'EDUCATION',
        'AWARDS',
        'Languages:'
    ]

    # Check if any sections are missing
    missing_sections = []
    for section in required_sections:
        if section in original_content and section not in formatted_content:
            missing_sections.append(section)

    # If sections are missing, extract and append them
    if missing_sections:
        # Find where to insert missing content
        insert_point = len(formatted_content)

        for section in missing_sections:
            # Extract the section from original content
            section_start = original_content.find(section)
            if section_start != -1:
                # Find the end of this section (next section or end of content)
                section_end = len(original_content)
                for other_section in required_sections:
                    if other_section != section:
                        other_start = original_content.find(other_section, section_start + 1)
                        if other_start != -1 and other_start < section_end:
                            section_end = other_start

                # Extract the section content
                section_content = original_content[section_start:section_end].strip()

                # Add to formatted content
                formatted_content += f"\n\n{section_content}"

    return formatted_content

def ensure_all_content_preserved(content: str) -> str:
    """
    Ensure ALL content is preserved - this is the top priority
    """
    # Simply return the content with minimal cleaning
    # Remove excessive whitespace but preserve all text
    lines = []
    for line in content.split('\n'):
        line = line.strip()
        if line:  # Only add non-empty lines
            lines.append(line)

    return '\n'.join(lines)

def apply_minimal_formatting(content: str) -> str:
    """
    Apply only the most basic formatting improvements
    """
    # Add proper header for name
    if content.startswith('Alex Guyenne'):
        content = '# ' + content

    # Add section headers where clearly missing
    content = re.sub(r'\n(WORK EXPERIENCE|PROJECT HIGHLIGHT|SKILLS & TOOLS|EDUCATION|AWARDS)\n',
                     r'\n\n## \1\n\n', content)

    # Clean up excessive line breaks
    content = re.sub(r'\n{3,}', '\n\n', content)

    return content
