"""
Simple, reliable PDF generator that preserves all content and formatting
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from io import BytesIO
import textwrap

def create_complete_resume_pdf(content: str) -> bytes:
    """
    Create a complete PDF with ALL content, properly formatted
    """
    buffer = BytesIO()
    p = canvas.Canvas(buffer, pagesize=letter)
    width, height = letter
    
    # Start position
    y_position = height - 50
    left_margin = 50
    line_height = 14
    
    # Split content into lines and process each one
    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            y_position -= line_height // 2  # Small space for empty lines
            continue
            
        # Check if we need a new page
        if y_position < 100:
            p.showPage()
            y_position = height - 50
        
        # Format based on content type
        if line.startswith('# '):
            # Main title (name)
            p.setFont("Helvetica-Bold", 16)
            text = line[2:].strip()
            p.drawString(left_margin, y_position, text)
            y_position -= line_height * 1.5
            
        elif 'Berlin, Germany' in line and '•' in line:
            # Contact information
            p.setFont("Helvetica", 10)
            p.drawString(left_margin, y_position, line)
            y_position -= line_height * 1.5
            
        elif line.startswith('**') and line.endswith('**'):
            # Job titles and section headers
            p.setFont("Helvetica-Bold", 11)
            text = line[2:-2].strip()
            p.drawString(left_margin, y_position, text)
            y_position -= line_height
            
        elif line.startswith('##'):
            # Section headers
            p.setFont("Helvetica-Bold", 12)
            text = line[2:].strip()
            y_position -= line_height // 2  # Extra space before section
            p.drawString(left_margin, y_position, text)
            y_position -= line_height
            
        elif line.isupper() and len(line) > 5:
            # All caps section headers
            p.setFont("Helvetica-Bold", 12)
            y_position -= line_height // 2  # Extra space before section
            p.drawString(left_margin, y_position, line)
            y_position -= line_height
            
        elif line.startswith('•'):
            # Bullet points
            p.setFont("Helvetica", 9)
            bullet_text = line[1:].strip()
            
            # Wrap long bullet points
            max_width = 80
            if len(bullet_text) > max_width:
                wrapped = textwrap.wrap(bullet_text, width=max_width)
                for i, wrapped_line in enumerate(wrapped):
                    if i == 0:
                        p.drawString(left_margin + 10, y_position, f"• {wrapped_line}")
                    else:
                        p.drawString(left_margin + 20, y_position, wrapped_line)
                    y_position -= line_height
                    if y_position < 100:
                        p.showPage()
                        y_position = height - 50
            else:
                p.drawString(left_margin + 10, y_position, f"• {bullet_text}")
                y_position -= line_height
                
        else:
            # Regular text
            p.setFont("Helvetica", 10)
            
            # Wrap long lines
            max_width = 85
            if len(line) > max_width:
                wrapped = textwrap.wrap(line, width=max_width)
                for wrapped_line in wrapped:
                    p.drawString(left_margin, y_position, wrapped_line)
                    y_position -= line_height
                    if y_position < 100:
                        p.showPage()
                        y_position = height - 50
            else:
                p.drawString(left_margin, y_position, line)
                y_position -= line_height
    
    p.save()
    pdf_bytes = buffer.getvalue()
    buffer.close()
    return pdf_bytes

def format_resume_for_pdf(content: str) -> str:
    """
    Pre-format the resume content for better PDF rendering
    """
    # Ensure we have all the content
    lines = content.split('\n')
    formatted_lines = []
    
    for line in lines:
        line = line.strip()
        if line:
            formatted_lines.append(line)
    
    return '\n'.join(formatted_lines)
