# Multi-stage Docker build for Resume Tailor Application
FROM node:18-alpine AS frontend-builder

# Set working directory for frontend
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy frontend source
COPY app/ ./app/
COPY next.config.js ./
COPY .env ./

# Build the Next.js application
RUN npm run build

# Production stage
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy Python requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend application
COPY web_app/ ./web_app/
COPY resume_generator.py .
COPY llm_service.py .
COPY .env .

# Copy built frontend from previous stage
COPY --from=frontend-builder /app/.next ./web_app/static/
COPY --from=frontend-builder /app/public ./web_app/static/public/

# Create non-root user for security
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start the application
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--workers", "2", "--timeout", "120", "web_app.app:app"]
