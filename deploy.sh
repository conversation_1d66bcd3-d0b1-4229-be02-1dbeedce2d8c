#!/bin/bash

# Resume Tailor Deployment Script
# This script helps deploy the application for user testing

set -e  # Exit on any error

echo "🚀 Resume Tailor Deployment Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    echo "Please create a .env file with your Together.ai API key:"
    echo "TOGETHER_API_KEY=your_api_key_here"
    echo "TOGETHER_MODEL=meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo"
    echo "FLASK_ENV=production"
    echo "FLASK_DEBUG=False"
    exit 1
fi

# Check if Together.ai API key is set
if ! grep -q "TOGETHER_API_KEY=" .env || grep -q "TOGETHER_API_KEY=your_together_ai_api_key_here" .env; then
    print_error "Please set your Together.ai API key in the .env file"
    exit 1
fi

print_success ".env file found and configured"

# Function to deploy with Docker
deploy_docker() {
    print_status "Deploying with Docker..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    # Build and start the application
    print_status "Building Docker image..."
    docker build -t resume-tailor .
    
    print_status "Starting application with Docker Compose..."
    docker-compose up -d
    
    # Wait for the application to start
    print_status "Waiting for application to start..."
    sleep 10
    
    # Check if the application is running
    if curl -f http://localhost:8080/ > /dev/null 2>&1; then
        print_success "Application is running successfully!"
        echo ""
        echo "🌐 Application URL: http://localhost:8080"
        echo "📊 Health Check: http://localhost:8080/"
        echo ""
        echo "To stop the application: docker-compose down"
        echo "To view logs: docker-compose logs -f"
    else
        print_error "Application failed to start. Check logs with: docker-compose logs"
        exit 1
    fi
}

# Function to deploy locally
deploy_local() {
    print_status "Deploying locally..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Install Python dependencies
    print_status "Installing Python dependencies..."
    pip3 install -r requirements.txt
    
    # Install Node.js dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    # Build frontend
    print_status "Building frontend..."
    npm run build
    
    # Start the application
    print_status "Starting application..."
    echo "Backend will run on http://localhost:8080"
    echo "Press Ctrl+C to stop"
    
    cd web_app
    python3 app.py
}

# Function to deploy to cloud (placeholder)
deploy_cloud() {
    print_warning "Cloud deployment options:"
    echo ""
    echo "1. 🐳 Docker Hub + Cloud Run:"
    echo "   - Push image: docker tag resume-tailor your-registry/resume-tailor"
    echo "   - Deploy to Google Cloud Run, AWS ECS, or Azure Container Instances"
    echo ""
    echo "2. 🚀 Heroku:"
    echo "   - heroku create your-app-name"
    echo "   - git push heroku main"
    echo ""
    echo "3. ☁️  Railway/Render/Fly.io:"
    echo "   - Connect your GitHub repository"
    echo "   - Set environment variables"
    echo "   - Deploy automatically"
    echo ""
    echo "4. 🔧 VPS/Server:"
    echo "   - Copy files to server"
    echo "   - Run: docker-compose up -d"
    echo ""
}

# Main menu
echo ""
echo "Choose deployment method:"
echo "1) 🐳 Docker (Recommended for testing)"
echo "2) 💻 Local development"
echo "3) ☁️  Cloud deployment info"
echo ""
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        deploy_docker
        ;;
    2)
        deploy_local
        ;;
    3)
        deploy_cloud
        ;;
    *)
        print_error "Invalid choice. Please run the script again."
        exit 1
        ;;
esac
