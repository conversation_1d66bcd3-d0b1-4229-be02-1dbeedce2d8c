"""
Minimal Resume Enhancement - Preserves Authenticity
This module provides conservative resume enhancement that maintains the original voice and structure.
"""

import re
from llm_service import llm_service

def enhance_resume_minimal(job_description: str, base_resume_content: str, key_achievements: list = None) -> dict:
    """
    Minimal resume enhancement that preserves authenticity and original voice.
    Only makes subtle strategic adjustments and fixes formatting issues.
    """
    if key_achievements is None:
        key_achievements = []

    # Start with the original content and fix formatting issues
    enhanced_content = fix_resume_formatting(base_resume_content)

    # Extract a few key terms from job description for strategic placement
    job_keywords = extract_key_terms(job_description)

    # Enhance job titles for ATS optimization
    enhanced_content = optimize_job_titles(enhanced_content, job_description)

    # Only enhance if LLM is available and we can make subtle improvements
    if llm_service.is_enabled():
        # Try to enhance the professional summary only
        summary_match = re.search(r'(Product-led.*?adoption[^.]*\.)', enhanced_content, re.DOTALL)
        if summary_match:
            original_summary = summary_match.group(1)
            enhanced_summary = llm_service.enhance_summary(original_summary, job_description, key_achievements)

            # Only replace if the enhancement is subtle and maintains authenticity
            if enhanced_summary and enhanced_summary != original_summary:
                # Check if the enhancement is too different (indicates over-enhancement)
                if calculate_similarity(original_summary, enhanced_summary) > 0.7:
                    enhanced_content = enhanced_content.replace(original_summary, enhanced_summary)

    # Calculate match score
    match_score = calculate_basic_match_score(job_description, enhanced_content)

    # Generate simple insights
    insights = generate_simple_insights(job_description, enhanced_content, job_keywords)

    return {
        'tailored_resume_markdown': enhanced_content,
        'match_score': match_score,
        'match_rationale': insights,
        'strategic_rationale': 'Minimal enhancement preserving your authentic voice and experience.'
    }

def extract_key_terms(job_description: str, max_terms: int = 5) -> list:
    """Extract key terms from job description without over-processing"""
    # Simple keyword extraction focusing on important terms
    text = job_description.lower()
    
    # Look for important terms
    important_terms = []
    
    # Technical terms
    tech_terms = ['ai', 'automation', 'digital', 'data', 'analytics', 'cloud', 'api', 'python', 'sql']
    for term in tech_terms:
        if term in text:
            important_terms.append(term)
    
    # Business terms  
    business_terms = ['strategy', 'sales', 'account', 'client', 'customer', 'growth', 'revenue', 'management']
    for term in business_terms:
        if term in text:
            important_terms.append(term)
    
    # Role-specific terms
    role_terms = ['consultant', 'manager', 'strategist', 'analyst', 'specialist', 'lead']
    for term in role_terms:
        if term in text:
            important_terms.append(term)
    
    return important_terms[:max_terms]

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate simple similarity between two texts"""
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def calculate_basic_match_score(job_description: str, resume_content: str) -> int:
    """Calculate a basic match score based on keyword overlap"""
    job_words = set(re.findall(r'\b\w+\b', job_description.lower()))
    resume_words = set(re.findall(r'\b\w+\b', resume_content.lower()))
    
    # Remove common words
    common_words = {'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by'}
    job_words = job_words - common_words
    resume_words = resume_words - common_words
    
    if not job_words:
        return 50
    
    overlap = job_words.intersection(resume_words)
    score = int((len(overlap) / len(job_words)) * 100)
    
    # Ensure score is reasonable
    return min(max(score, 30), 85)

def generate_simple_insights(job_description: str, resume_content: str, keywords: list) -> str:
    """Generate simple, actionable insights"""
    match_score = calculate_basic_match_score(job_description, resume_content)
    
    insights = f"""Match Score: {match_score}/100

Key Observations:
• Your resume shows good alignment with the role requirements
• Your experience in AI and automation is highly relevant
• Consider naturally incorporating these terms where appropriate: {', '.join(keywords[:3])}

Strengths:
• Strong technical background in AI and automation tools
• Proven track record in client-facing roles
• Experience with cross-functional collaboration

Recommendations:
• Your authentic experience speaks for itself
• Focus on quantifiable achievements where possible
• Ensure your unique voice and personality come through

Note: This analysis preserves your authentic voice while identifying strategic alignment opportunities."""
    
    return insights

def fix_resume_formatting(content: str) -> str:
    """Fix common formatting issues in the resume"""

    # Fix contact information formatting
    content = fix_contact_formatting(content)

    # Fix education section formatting
    content = fix_education_formatting(content)

    # Fix work experience formatting
    content = fix_work_experience_formatting(content)

    # Fix project highlights formatting
    content = fix_project_highlights_formatting(content)

    # Clean up general formatting issues
    content = clean_general_formatting(content)

    return content

def fix_contact_formatting(content: str) -> str:
    """Fix contact information formatting"""
    # Fix phone number formatting
    content = re.sub(r'•\s*(\+\d+)', r'\n• \1', content)

    # Fix email formatting
    content = re.sub(r'•\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', r'\n• \1', content)

    # Fix LinkedIn formatting
    content = re.sub(r'•\s*(linkedin\.com/in/[a-zA-Z0-9-]+)', r'\n• \1', content)

    return content

def fix_education_formatting(content: str) -> str:
    """Fix education section formatting issues"""
    # Fix broken "Master's" and "Bachelor's"
    content = re.sub(r"Master'\s*s\s+in", "Master's in", content)
    content = re.sub(r"Bachelor'\s*s\s+in", "Bachelor's in", content)

    # Fix university formatting
    content = re.sub(r'(Master\'s in [^•\n]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)
    content = re.sub(r'(Bachelor\'s in [^•\n]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)

    return content

def fix_work_experience_formatting(content: str) -> str:
    """Fix work experience formatting"""
    # Fix job title formatting for better ATS optimization
    # Pattern: Job Title | Company, Location | Dates

    # Fix Account Manager entry
    content = re.sub(r'Account Manager \|\s*AI automation\s*Gladtobe\s*•\s*Stuttgart/Berlin\s+(\d{2}/\d{4})\s*-\s*(Present|\d{2}/\d{4})',
                     r'**Account Manager - AI Automation** | Gladtobe, Stuttgart/Berlin | \1 - \2', content)

    # Fix AI Solutions Consultant entry
    content = re.sub(r'AI Solutions Consultant\s+Freelance/Consulting\s+(\d{2}/\d{4})\s*-\s*(Present|\d{2}/\d{4})',
                     r'**AI Solutions Consultant** | Freelance/Consulting | \1 - \2', content)

    # Fix other job entries
    content = re.sub(r'(Digital Experience Designer)\s*([A-Z][a-zA-Z\s]+)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)

    content = re.sub(r'(Project Lead Strategy and Innovation)\s*([A-Z][a-zA-Z\s&]+)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'**\1** | \2, \3 | \4 - \5', content)

    content = re.sub(r'(Design & Research Innovation Lead)\s*([A-Z][a-zA-Z\s&]+)\s+(\d{2}/\d{4})\s*-\s*(\d{2}/\d{4})',
                     r'**\1** | \2 | \3 - \4', content)

    return content

def fix_project_highlights_formatting(content: str) -> str:
    """Fix project highlights formatting"""
    # Add proper line breaks between projects
    content = re.sub(r'(Ad Intelligence Tracker[^A-Z]+)(AI Lead Gen Tool)', r'\1\n\n**\2', content)
    content = re.sub(r'(AI Lead Gen Tool[^A-Z]+)(Modular AI Content Generator)', r'\1\n\n**\2', content)

    # Format project titles
    content = re.sub(r'Ad Intelligence Tracker', r'**Ad Intelligence Tracker**', content)
    content = re.sub(r'AI Lead Gen Tool for B2B Niche Validation', r'**AI Lead Gen Tool for B2B Niche Validation**', content)
    content = re.sub(r'Modular AI Content Generator for Telecom', r'**Modular AI Content Generator for Telecom**', content)

    return content

def clean_general_formatting(content: str) -> str:
    """Clean up general formatting issues"""
    # Fix multiple spaces
    content = re.sub(r' +', ' ', content)

    # Fix line breaks
    content = re.sub(r'\n{3,}', '\n\n', content)

    # Fix bullet points
    content = re.sub(r'•\s*([^•\n]+)', r'• \1', content)

    return content.strip()

def optimize_job_titles(content: str, job_description: str) -> str:
    """Optimize job titles for better ATS matching"""

    # Extract key role terms from job description
    role_keywords = extract_role_keywords(job_description)

    # Current professional title optimization
    current_title = "AI Sales Strategist | Product-Led Builder | Automation Enablement"

    # Create ATS-optimized version based on job description
    if any(keyword in job_description.lower() for keyword in ['sales', 'account', 'business development']):
        optimized_title = "AI Sales Strategist | Account Management | Business Development"
    elif any(keyword in job_description.lower() for keyword in ['consultant', 'consulting', 'advisory']):
        optimized_title = "AI Solutions Consultant | Digital Strategy | Automation Expert"
    elif any(keyword in job_description.lower() for keyword in ['product', 'manager', 'product manager']):
        optimized_title = "AI Product Strategist | Digital Innovation | Automation Solutions"
    elif any(keyword in job_description.lower() for keyword in ['engineer', 'technical', 'developer']):
        optimized_title = "AI Solutions Engineer | Technical Strategy | Automation Development"
    else:
        # Keep original but make it more ATS-friendly
        optimized_title = "AI Strategist | Digital Innovation | Automation Solutions"

    # Replace the title if it's significantly different and relevant
    if optimized_title != current_title:
        content = content.replace(current_title, optimized_title)

    return content

def extract_role_keywords(job_description: str) -> list:
    """Extract role-specific keywords from job description"""
    role_terms = []
    text = job_description.lower()

    # Common role keywords
    role_keywords = [
        'sales', 'account manager', 'business development', 'consultant', 'consulting',
        'product manager', 'product', 'strategy', 'strategist', 'engineer', 'technical',
        'developer', 'analyst', 'specialist', 'lead', 'director', 'manager'
    ]

    for keyword in role_keywords:
        if keyword in text:
            role_terms.append(keyword)

    return role_terms
