(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9411:e=>{"use strict";e.exports=require("node:path")},7742:e=>{"use strict";e.exports=require("node:process")},1041:e=>{"use strict";e.exports=require("node:url")},7310:e=>{"use strict";e.exports=require("url")},3721:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(7096),s=r(6132),o=r(7284),i=r.n(o),n=r(2564),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4985)),"/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,8489)),"/Users/<USER>/Documents/augment-projects/Resume custom/app/layout.js"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9291,23)),"next/dist/client/components/not-found-error"]}],d=["/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js"],p="/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},659:(e,t,r)=>{Promise.resolve().then(r.bind(r,6174))},6174:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Home});var a=r(784),s=r(9885),o=r(663),i=r(9814),n=r.n(i);function Home(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(""),[l,c]=(0,s.useState)(""),[d,p]=(0,s.useState)(""),[u,m]=(0,s.useState)(null),[h,_]=(0,s.useState)(!1),[x,g]=(0,s.useState)(null),[j,f]=(0,s.useState)(!1),[w,v]=(0,s.useState)(""),handleSubmit=async t=>{t.preventDefault(),_(!0),g(null),m(null);try{let t=await fetch("/api/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({job_description:e,base_resume_content:r,key_achievements:l,specific_concerns:d})});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let a=await t.json();if(a.error)throw Error(a.error);m(a)}catch(e){console.error("Error:",e),g(e.message||"An error occurred while generating the resume")}finally{_(!1)}},handleCopyToClipboard=async()=>{if(u?.tailored_resume_markdown)try{await navigator.clipboard.writeText(u.tailored_resume_markdown),f(!0),setTimeout(()=>f(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}},handleFileUpload=async e=>{let t=e.target.files[0];if(t){g(null),v("Processing file...");try{if(t.size>10485760)throw Error("File size too large. Please upload a file smaller than 10MB.");let e=t.name.toLowerCase();if(!e.endsWith(".pdf")&&!e.endsWith(".txt"))throw Error("Unsupported file type. Please upload a PDF or text file.");v("Uploading and processing file...");let r=new FormData;r.append("file",t);let a=await fetch("/api/upload",{method:"POST",body:r});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to process file")}let s=await a.json();if(!s.text||!s.text.trim())throw Error("No text content found in the file. Please check your file and try again.");i(s.text),v("File processed successfully!"),setTimeout(()=>v(""),2e3)}catch(e){console.error("File upload error:",e),g(e.message),v("")}}};return(0,a.jsxs)("main",{className:n().main,children:[a.jsx("h1",{className:n().title,children:"Resume Tailor"}),a.jsx("p",{className:n().description,children:"Optimize your resume for specific job applications"}),(0,a.jsxs)("form",{onSubmit:handleSubmit,className:n().form,children:[(0,a.jsxs)("div",{className:n().inputGroup,children:[a.jsx("label",{htmlFor:"jobDescription",children:"Job Description *"}),a.jsx("textarea",{id:"jobDescription",value:e,onChange:e=>t(e.target.value),placeholder:"Paste the full job description here...",required:!0,rows:8})]}),(0,a.jsxs)("div",{className:n().inputGroup,children:[a.jsx("label",{htmlFor:"resumeContent",children:"Your Current Resume *"}),(0,a.jsxs)("div",{className:n().fileUploadSection,children:[a.jsx("textarea",{id:"resumeContent",value:r,onChange:e=>i(e.target.value),placeholder:"Paste your current resume content here...",required:!0,rows:8}),(0,a.jsxs)("div",{className:n().fileUploadOption,children:[a.jsx("span",{children:"Or upload a file:"}),a.jsx("input",{type:"file",accept:".pdf,.txt",onChange:handleFileUpload,className:n().fileInput}),a.jsx("span",{className:n().fileHint,children:"Supports PDF and text files (max 10MB)"})]}),w&&a.jsx("div",{className:n().uploadProgress,children:w})]})]}),(0,a.jsxs)("div",{className:n().inputGroup,children:[a.jsx("label",{htmlFor:"keyAchievements",children:"Key Achievements (Optional)"}),a.jsx("textarea",{id:"keyAchievements",value:l,onChange:e=>c(e.target.value),placeholder:"List 1-3 key achievements you want to highlight for this specific job...",rows:4})]}),(0,a.jsxs)("div",{className:n().inputGroup,children:[a.jsx("label",{htmlFor:"specificConcerns",children:"Specific Concerns (Optional)"}),a.jsx("textarea",{id:"specificConcerns",value:d,onChange:e=>p(e.target.value),placeholder:"Any specific parts of your resume you want to improve or aspects of the job you want to emphasize...",rows:3})]}),a.jsx("button",{type:"submit",className:n().button,disabled:h,children:h?"Generating Tailored Resume...":"Generate Tailored Resume"})]}),x&&(0,a.jsxs)("div",{className:n().error,children:[a.jsx("h3",{children:"Error"}),a.jsx("p",{children:x})]}),u&&(0,a.jsxs)("div",{className:n().result,children:[a.jsx("h2",{children:"Your Tailored Resume"}),(0,a.jsxs)("div",{className:n().scoreCard,children:[(0,a.jsxs)("h3",{children:["Match Score: ",u.match_score,"/100"]}),a.jsx("div",{className:n().rationale,children:a.jsx(o.UG,{children:u.match_rationale})})]}),u.strategic_rationale&&(0,a.jsxs)("div",{className:n().strategicRationale,children:[a.jsx("h3",{children:"Strategic Rationale"}),a.jsx(o.UG,{children:u.strategic_rationale})]}),(0,a.jsxs)("div",{className:n().resumePreview,children:[(0,a.jsxs)("div",{className:n().previewHeader,children:[a.jsx("h3",{children:"Resume Preview"}),(0,a.jsxs)("div",{className:n().actionButtons,children:[a.jsx("button",{className:n().copyButton,onClick:handleCopyToClipboard,title:"Copy to clipboard",children:j?"✓ Copied!":"\uD83D\uDCCB Copy"}),a.jsx("button",{className:n().downloadButton,onClick:()=>{let e=new Blob([u.tailored_resume_markdown],{type:"text/markdown"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="tailored_resume.md",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)},title:"Download as Markdown",children:"\uD83D\uDCE5 Download MD"})]})]}),a.jsx("div",{className:n().markdownPreview,children:a.jsx(o.UG,{children:u.tailored_resume_markdown})}),(0,a.jsxs)("div",{className:n().rawMarkdown,children:[a.jsx("h4",{children:"Raw Markdown (for copying)"}),a.jsx("pre",{className:n().markdownRaw,children:u.tailored_resume_markdown})]})]})]})]})}},9814:e=>{e.exports={main:"page_main__nw1Wk",title:"page_title__po7na",description:"page_description__lvaOp",form:"page_form__NxPAx",inputGroup:"page_inputGroup__keP_G",fileUploadSection:"page_fileUploadSection__h651M",fileUploadOption:"page_fileUploadOption__lJq5z",fileInput:"page_fileInput__3Qiq7",fileHint:"page_fileHint__6NIeM",uploadProgress:"page_uploadProgress__1s8yN",button:"page_button__52WaL",error:"page_error__JBrsB",result:"page_result__7WhcO",scoreCard:"page_scoreCard__pEla_",rationale:"page_rationale__E_Uco",strategicRationale:"page_strategicRationale__zwoxY",previewHeader:"page_previewHeader__nnSzV",actionButtons:"page_actionButtons__aac_D",copyButton:"page_copyButton__UNIa2",downloadButton:"page_downloadButton__lvXC9",markdownPreview:"page_markdownPreview__w7jd6",rawMarkdown:"page_rawMarkdown__xjVcb",markdownRaw:"page_markdownRaw__IVI0h"}},4985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>l});var a=r(5153);let s=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/augment-projects/Resume custom/app/page.js`),{__esModule:o,$$typeof:i}=s,n=s.default,l=n}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[472,663,950],()=>__webpack_exec__(3721));module.exports=r})();