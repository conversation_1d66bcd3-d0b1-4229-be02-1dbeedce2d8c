"""
Professional PDF generator that matches <PERSON>'s original resume format exactly
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import black
from io import BytesIO
import textwrap
import re

def create_complete_resume_pdf(content: str) -> bytes:
    """
    Create a professional PDF that matches <PERSON>'s original resume format exactly
    """
    buffer = BytesIO()
    p = canvas.Canvas(buffer, pagesize=letter)
    width, height = letter

    # Professional margins and spacing
    left_margin = 72  # 1 inch
    right_margin = width - 72
    top_margin = height - 72
    y_position = top_margin

    # Render the resume with exact original formatting
    y_position = render_original_format(p, content, left_margin, right_margin, y_position, width, height)

    p.save()
    pdf_bytes = buffer.getvalue()
    buffer.close()
    return pdf_bytes

def render_original_format(p, content, left_margin, right_margin, y_position, width, height):
    """Render resume with exact original formatting"""

    # 1. NAME - Large, bold, centered
    p.setFont("Helvetica-Bold", 20)
    name = "<PERSON>"
    name_width = p.stringWidth(name, "Helvetica-Bold", 20)
    x_center = (width - name_width) / 2
    p.drawString(x_center, y_position, name)
    y_position -= 25

    # 2. CONTACT INFO - Centered, smaller, with bullets
    p.setFont("Helvetica", 10)
    contact = "Berlin, Germany • +4915774654068 • <EMAIL> • linkedin.com/in/alexguyenne"
    contact_width = p.stringWidth(contact, "Helvetica", 10)
    x_center = (width - contact_width) / 2
    p.drawString(x_center, y_position, contact)
    y_position -= 25

    # 3. PROFESSIONAL TITLE - Centered, bold
    p.setFont("Helvetica-Bold", 12)
    title = "AI Sales Strategist | Product-Led Builder | Automation Enablement"
    title_width = p.stringWidth(title, "Helvetica-Bold", 12)
    x_center = (width - title_width) / 2
    p.drawString(x_center, y_position, title)
    y_position -= 25

    # 4. SUMMARY - Justified paragraph
    p.setFont("Helvetica", 10)
    summary = "Product-led, AI-fluent, and systems-minded. I design and prototype generative AI tools that help businesses explore automation, improve workflows, and test go-to-market strategies. With a background in UX and emerging tech, I bridge product thinking and commercial insight to shape pilots, support solution discovery, and lay the foundation for scalable adoption"

    # Wrap summary text
    wrapped_summary = textwrap.fill(summary, width=85)
    for line in wrapped_summary.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 14

    y_position -= 10  # Extra space after summary

    # 5. WORK EXPERIENCE SECTION
    y_position = render_work_experience(p, left_margin, y_position, width, height)

    # 6. PROJECT HIGHLIGHTS SECTION
    y_position = render_project_highlights(p, left_margin, y_position, width, height)

    # 7. SKILLS & TOOLS SECTION
    y_position = render_skills_section(p, left_margin, y_position, width, height)

    # 8. EDUCATION SECTION
    y_position = render_education_section(p, left_margin, y_position, width, height)

    # 9. AWARDS SECTION
    y_position = render_awards_section(p, left_margin, y_position, width, height)

    return y_position

def render_work_experience(p, left_margin, y_position, width, height):
    """Render work experience section with exact original formatting"""

    # Check for new page
    if y_position < 200:
        p.showPage()
        y_position = height - 72

    # Section header
    p.setFont("Helvetica-Bold", 12)
    p.drawString(left_margin, y_position, "WORK EXPERIENCE")
    y_position -= 20

    # Job 1: Account Manager
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Account Manager | AI automation")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "Gladtobe • Stuttgart/Berlin 01/2025 - Present")
    y_position -= 16

    # Bullet points
    bullets = [
        "Stepping into a cross-functional role focused on scaling digital campaign operations and client success across fintech, e-commerce, and healthtech accounts",
        "Enhancing campaign delivery with AI-assisted process design and rapid experimentation alongside strategy and creative teams",
        "On track to manage a €2M+ annual account portfolio; engaging with C-level stakeholders to align performance goals and reporting frameworks"
    ]

    for bullet in bullets:
        wrapped = textwrap.fill(f"• {bullet}", width=85)
        for line in wrapped.split('\n'):
            p.drawString(left_margin, y_position, line)
            y_position -= 12

    y_position -= 8  # Space between jobs

    # Job 2: AI Solutions Consultant
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "AI Solutions Consultant")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "Freelance/Consulting 04/2024 - Present")
    y_position -= 16

    bullets = [
        "Developed early solutions for ad intelligence dashboards, onboarding flows, and campaign content assistants for SaaS and Telecom clients",
        "Initiated and co-led feasibility testing for generative AI use cases with product teams across B2B domains",
        "Helped clients explore AI solution framing, internal upskilling, and go-to-market angles during early concept validation phases"
    ]

    for bullet in bullets:
        wrapped = textwrap.fill(f"• {bullet}", width=85)
        for line in wrapped.split('\n'):
            p.drawString(left_margin, y_position, line)
            y_position -= 12

    y_position -= 8

    # Job 3: Digital Experience Designer
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Digital Experience Designer")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "ReflectOn • Berlin 07/2023 - 02/2024")
    y_position -= 16

    bullets = [
        "Explored AI-powered personalization in a mental health SaaS onboarding flow, contributing to higher completion and engagement",
        "Simplified complex digital flows to improve product usability and reduced user drop-off across key journeys"
    ]

    for bullet in bullets:
        wrapped = textwrap.fill(f"• {bullet}", width=85)
        for line in wrapped.split('\n'):
            p.drawString(left_margin, y_position, line)
            y_position -= 12

    y_position -= 8

    # Job 4: Project Lead Strategy and Innovation
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Project Lead Strategy and Innovation")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "Bewatec Connected Care GmbH • Berlin 01/2022 - 03/2023")
    y_position -= 16

    bullets = [
        "Led internal discovery sprints and product exploration efforts, contributing to new client engagement formats and operational playbooks",
        "Facilitated design workshops and roadmap framing with hospital clients to align technology potential with procurement goals",
        "Delivered process redesigns that improved internal efficiency and team satisfaction across pilot teams"
    ]

    for bullet in bullets:
        wrapped = textwrap.fill(f"• {bullet}", width=85)
        for line in wrapped.split('\n'):
            p.drawString(left_margin, y_position, line)
            y_position -= 12

    y_position -= 8

    # Job 5: Design & Research Innovation Lead
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Design & Research Innovation Lead")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "Bewatec Connected Care GmbH 07/2019 - 12/2021")
    y_position -= 16

    bullets = [
        "Designed enterprise-facing digital workflows and clinical support tools used by hospital staff in EU-wide deployments",
        "Aligned product decisions with frontline use cases and user insights, improving retention and procurement feedback cycles"
    ]

    for bullet in bullets:
        wrapped = textwrap.fill(f"• {bullet}", width=85)
        for line in wrapped.split('\n'):
            p.drawString(left_margin, y_position, line)
            y_position -= 12

    y_position -= 15  # Extra space after section

    return y_position

def render_project_highlights(p, left_margin, y_position, width, height):
    """Render project highlights section"""

    # Check for new page
    if y_position < 150:
        p.showPage()
        y_position = height - 72

    # Section header
    p.setFont("Helvetica-Bold", 12)
    p.drawString(left_margin, y_position, "PROJECT HIGHLIGHT")
    y_position -= 20

    # Project 1
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Ad Intelligence Tracker")
    y_position -= 14

    p.setFont("Helvetica", 10)
    description = "Created a real-time competitor ad parsing tool using Firecrawl and n8n; structured analytics into Airtable"
    wrapped = textwrap.fill(description, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    y_position -= 8

    # Project 2
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "AI Lead Gen Tool for B2B Niche Validation")
    y_position -= 14

    p.setFont("Helvetica", 10)
    description = "Built early-stage scraping and enrichment tool to test B2B niche segmentation hypotheses"
    wrapped = textwrap.fill(description, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    y_position -= 8

    # Project 3
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Modular AI Content Generator for Telecom")
    y_position -= 14

    p.setFont("Helvetica", 10)
    description = "Developed a modular content assistant for telecom to generate blog, newsletter, and email blocks tailored to campaign needs"
    wrapped = textwrap.fill(description, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    y_position -= 15

    return y_position

def render_skills_section(p, left_margin, y_position, width, height):
    """Render skills and tools section"""

    # Check for new page
    if y_position < 100:
        p.showPage()
        y_position = height - 72

    # Section header
    p.setFont("Helvetica-Bold", 12)
    p.drawString(left_margin, y_position, "SKILLS & TOOLS")
    y_position -= 20

    # Skills text
    p.setFont("Helvetica", 10)
    skills = "GPT, Claude, n8n, Firecrawl, AI workflow design, Enterprise SaaS sales, pipeline development, C-level pitching, go-to-market planning, content automation, account growth, multi-market scaling, HubSpot, Notion, Airtable, Supabase, Fireflies, Slack, Mixpanel"
    wrapped = textwrap.fill(skills, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    y_position -= 8

    # Languages
    p.setFont("Helvetica", 10)
    languages = "Languages: English (Fluent), French (Native), German (Limited professional proficiency)"
    wrapped = textwrap.fill(languages, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    y_position -= 15

    return y_position

def render_education_section(p, left_margin, y_position, width, height):
    """Render education section"""

    # Check for new page
    if y_position < 80:
        p.showPage()
        y_position = height - 72

    # Section header
    p.setFont("Helvetica-Bold", 12)
    p.drawString(left_margin, y_position, "EDUCATION")
    y_position -= 20

    # Master's degree
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Master's in Computer and Network")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "Branly University • France 01/2011 - 12/2013")
    y_position -= 16

    # Bachelor's degree
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Bachelor's in Telecommunication and Network")
    y_position -= 14

    p.setFont("Helvetica", 10)
    p.drawString(left_margin, y_position, "University of Savoie • France 01/2009 - 12/2010")
    y_position -= 16

    y_position -= 15

    return y_position

def render_awards_section(p, left_margin, y_position, width, height):
    """Render awards section"""

    # Check for new page
    if y_position < 60:
        p.showPage()
        y_position = height - 72

    # Section header
    p.setFont("Helvetica-Bold", 12)
    p.drawString(left_margin, y_position, "AWARDS")
    y_position -= 20

    # Award
    p.setFont("Helvetica-Bold", 11)
    p.drawString(left_margin, y_position, "Winner – Circularity Hub Hackathon (2024)")
    y_position -= 14

    p.setFont("Helvetica", 10)
    description = "Led 'Dclutter' to 1st place among 30+ teams at Circularity Hub Hackathon, building an AI-powered circular economy matching platform in 48 hours"
    wrapped = textwrap.fill(description, width=85)
    for line in wrapped.split('\n'):
        p.drawString(left_margin, y_position, line)
        y_position -= 12

    return y_position

# Old parsing functions removed - using direct formatting approach now

def format_resume_for_pdf(content: str) -> str:
    """
    Pre-format the resume content to match Alex's original structure exactly
    """
    # Clean and structure the content to match original format
    lines = content.split('\n')
    formatted_lines = []

    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue

        # Handle name and contact info
        if 'Alex Guyenne' in line:
            # Extract name cleanly
            if 'Berlin' in line:
                name_part = line.split('Berlin')[0].strip()
                contact_part = 'Berlin' + line.split('Berlin')[1]
                formatted_lines.append(name_part)
                formatted_lines.append(contact_part)
            else:
                formatted_lines.append(line)
            i += 1
            continue

        # Handle professional title
        if 'AI Sales Strategist' in line and '|' in line:
            formatted_lines.append(line)
            i += 1
            continue

        # Handle summary
        if line.startswith('Product-led'):
            # Collect full summary
            summary_parts = [line]
            i += 1
            while i < len(lines) and lines[i].strip() and not lines[i].strip().isupper():
                summary_parts.append(lines[i].strip())
                i += 1

            # Join summary into proper paragraphs
            full_summary = ' '.join(summary_parts)
            # Break into readable lines
            import textwrap
            wrapped_summary = textwrap.fill(full_summary, width=90)
            formatted_lines.append(wrapped_summary)
            continue

        # Handle section headers
        if line.isupper() and len(line) > 5:
            formatted_lines.append('')  # Add space before section
            formatted_lines.append(line)
            i += 1
            continue

        # Handle job entries
        if any(title in line for title in ['Account Manager', 'AI Solutions Consultant', 'Digital Experience Designer', 'Project Lead', 'Design & Research']):
            formatted_lines.append('')  # Space before job
            formatted_lines.append(line)

            # Look for company/date info on next lines
            i += 1
            while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('•'):
                company_line = lines[i].strip()
                if company_line:
                    formatted_lines.append(company_line)
                i += 1
            continue

        # Handle project titles
        if any(project in line for project in ['Ad Intelligence Tracker', 'AI Lead Gen Tool', 'Modular AI Content']):
            formatted_lines.append('')  # Space before project
            formatted_lines.append(line)
            i += 1
            continue

        # Handle education entries
        if "Master's" in line or "Bachelor's" in line:
            formatted_lines.append('')  # Space before education
            formatted_lines.append(line)

            # Look for university info
            i += 1
            while i < len(lines) and lines[i].strip() and '•' in lines[i]:
                formatted_lines.append(lines[i].strip())
                i += 1
            continue

        # Handle everything else
        formatted_lines.append(line)
        i += 1

    return '\n'.join(formatted_lines)
