from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import sys
import os
import io
import re
import PyPDF2
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the sys.path to import resume_generator
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from resume_generator import generate_resume
from minimal_resume_enhancer import enhance_resume_minimal
from pdf_generator import generate_pdf_from_markdown

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def index():
    # Serve the main web interface
    return render_template('index.html')

@app.route('/api')
def api_info():
    return jsonify({
        'message': 'Resume Tailor API is running',
        'version': '1.0.0',
        'status': 'healthy',
        'endpoints': ['/generate', '/upload', '/debug-parse', '/health'],
        'llm_enabled': check_llm_status()
    })

@app.route('/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Basic health checks
        health_status = {
            'status': 'healthy',
            'timestamp': str(datetime.now()),
            'version': '1.0.0',
            'llm_service': check_llm_status(),
            'dependencies': {
                'flask': True,
                'together_ai': check_llm_status()
            }
        }
        return jsonify(health_status), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': str(datetime.now())
        }), 500

def check_llm_status():
    """Check if LLM service is available"""
    try:
        from llm_service import LLMService
        llm = LLMService()
        return llm.is_enabled()
    except Exception:
        return False

@app.route('/debug-parse', methods=['POST'])
def debug_parse():
    """Debug endpoint to see how resume content is being parsed"""
    try:
        data = request.get_json()
        content = data.get('content', '')

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        # Import the parsing function
        from resume_generator import parse_resume_content

        # Parse the content
        parsed = parse_resume_content(content)

        # Return detailed parsing results
        return jsonify({
            'original_content': content,
            'parsed_sections': parsed,
            'content_lines': content.split('\n')[:20],  # First 20 lines for debugging
            'total_lines': len(content.split('\n'))
        })

    except Exception as e:
        app.logger.error(f'Error in debug parsing: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file size (max 10MB)
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({'error': 'File size too large. Maximum size is 10MB.'}), 400

        # Extract text based on file type
        filename = file.filename.lower()
        extracted_text = ''

        if filename.endswith('.pdf'):
            extracted_text = extract_text_from_pdf(file)
        elif filename.endswith('.txt'):
            extracted_text = file.read().decode('utf-8')
        else:
            return jsonify({'error': 'Unsupported file type. Please upload a PDF or text file.'}), 400

        if not extracted_text.strip():
            return jsonify({'error': 'No text content found in the file.'}), 400

        return jsonify({'text': extracted_text})

    except Exception as e:
        app.logger.error(f'Error processing file: {str(e)}')
        return jsonify({'error': 'Failed to process file'}), 500

def extract_text_from_pdf(file):
    """Extract text from PDF file using PyPDF2 with better structure preservation"""
    try:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ''

        for page in pdf_reader.pages:
            page_text = page.extract_text()
            text += page_text + '\n'

        # Improve text structure by adding line breaks at logical points
        text = improve_pdf_text_structure(text)
        return text

    except Exception as e:
        raise Exception(f'Failed to extract text from PDF: {str(e)}')

def improve_pdf_text_structure(text):
    """Improve PDF text structure specifically for Alex's resume format"""

    # First, normalize whitespace but preserve some structure
    text = re.sub(r'\s+', ' ', text).strip()

    # Add line breaks before major section headers
    section_patterns = [
        r'(WORK EXPERIENCE)',
        r'(SKILLS & TOOLS)',
        r'(PROJECT HIGHLIGHT)',
        r'(EDUCATION)',
        r'(AWARDS)',
        r'(Languages:)'
    ]

    for pattern in section_patterns:
        text = re.sub(pattern, r'\n\n\1\n', text, flags=re.IGNORECASE)

    # Fix specific patterns in Alex's resume

    # Fix job title patterns - handle cases like "Account Manager | AI automation Gladtobe"
    text = re.sub(r'(Account Manager \|)\s*(AI automation)\s*(Gladtobe)', r'\n\n\1 \2\n\3', text)
    text = re.sub(r'(AI Solutions Consultant)\s*•\s*(Freelancer)', r'\n\n\1\n\2', text)
    text = re.sub(r'(Digital Experience Designer)\s*(ReflectOn)', r'\n\n\1\n\2', text)
    text = re.sub(r'(Project Lead)\s*(Strategy and Innovation)\s*(Bewatec)', r'\n\n\1 \2\n\3', text)
    text = re.sub(r'(Design & Research Innovation Lead)\s*(Bewatec)', r'\n\n\1\n\2', text)

    # Add line breaks before company info with location and dates
    # Pattern: Company • Location Date - Date
    text = re.sub(r'([A-Za-z\s&]+)\s*•\s*([A-Za-z\s,/]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(Present|\d{2}/\d{4}|\d{4})',
                  r'\n\1 • \2 \3 - \4\n', text)

    # Add line breaks before bullet points
    text = re.sub(r'\s*•\s*([^•]+)', r'\n• \1', text)

    # Fix education entries
    text = re.sub(r"(Master'?s? in [^•]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})",
                  r'\n\1\n\2 • \3 \4 - \5', text)
    text = re.sub(r"(Bachelor'?s? in [^•]+)\s*([A-Z][a-z\s]+University)\s*•\s*([A-Za-z\s]+)\s+(\d{2}/\d{4}|\d{4})\s*-\s*(\d{2}/\d{4}|\d{4})",
                  r'\n\1\n\2 • \3 \4 - \5', text)

    # Clean up multiple consecutive line breaks
    text = re.sub(r'\n{3,}', '\n\n', text)

    # Clean up spaces around line breaks
    text = re.sub(r' *\n *', '\n', text)

    return text.strip()

@app.route('/generate', methods=['POST'])
def generate():
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        job_description = data.get('job_description', '').strip()
        base_resume_content = data.get('base_resume_content', '').strip()
        key_achievements = data.get('key_achievements', '')  # Don't strip here - handle below
        specific_concerns = data.get('specific_concerns', '').strip()

        # Validation
        if not job_description:
            return jsonify({'error': 'Job description is required'}), 400

        if not base_resume_content:
            return jsonify({'error': 'Base resume content is required'}), 400

        # Convert key_achievements to list - handle both string and list inputs
        if isinstance(key_achievements, str) and key_achievements:
            key_achievements = [achievement.strip() for achievement in key_achievements.split('\n') if achievement.strip()]
        elif isinstance(key_achievements, list):
            # If it's already a list, clean up any empty strings
            key_achievements = [achievement.strip() for achievement in key_achievements if achievement and achievement.strip()]
        else:
            key_achievements = []

        # Use minimal enhancement to preserve authenticity
        result = enhance_resume_minimal(
            job_description=job_description,
            base_resume_content=base_resume_content,
            key_achievements=key_achievements
        )

        return jsonify(result)

    except ValueError as e:
        return jsonify({'error': f'Invalid input: {str(e)}'}), 400
    except Exception as e:
        app.logger.error(f'Error generating resume: {str(e)}')
        return jsonify({'error': 'An internal error occurred while generating the resume'}), 500

@app.route('/export-pdf', methods=['POST'])
def export_pdf():
    """Export resume as PDF"""
    try:
        data = request.get_json()

        if not data or 'resume_content' not in data:
            return jsonify({'error': 'Resume content is required'}), 400

        resume_content = data['resume_content']

        # Try to generate PDF
        try:
            pdf_bytes = generate_pdf_from_markdown(resume_content)
        except Exception as pdf_error:
            app.logger.error(f'PDF generation failed: {pdf_error}')
            # Return a simple text-based PDF as fallback
            pdf_bytes = create_simple_text_pdf(resume_content)

        # Return PDF as downloadable file
        from flask import make_response
        response = make_response(pdf_bytes)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = 'attachment; filename=resume_alex_guyenne.pdf'

        return response

    except Exception as e:
        app.logger.error(f'PDF export error: {e}')
        return jsonify({'error': f'PDF export failed: {str(e)}'}), 500

def create_simple_text_pdf(content: str) -> bytes:
    """Create a complete, properly formatted PDF with all content"""
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from io import BytesIO
        import textwrap

        buffer = BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        width, height = letter

        # Split content into clean lines
        lines = []
        for line in content.split('\n'):
            line = line.strip()
            if line:
                lines.append(line)

        y_position = height - 50
        page_num = 1

        for line in lines:
            # Check if we need a new page
            if y_position < 80:
                p.showPage()
                y_position = height - 50
                page_num += 1

            # Determine font and size based on content
            if line.startswith('# '):
                # Main title
                p.setFont("Helvetica-Bold", 18)
                text = line[2:].strip()
                p.drawString(50, y_position, text)
                y_position -= 25

            elif 'Berlin, Germany •' in line:
                # Contact info
                p.setFont("Helvetica", 10)
                p.drawString(50, y_position, line)
                y_position -= 20

            elif line.startswith('**') and line.endswith('**'):
                # Job titles or section headers
                p.setFont("Helvetica-Bold", 12)
                text = line[2:-2].strip()
                p.drawString(50, y_position, text)
                y_position -= 18

            elif line.startswith('##') or (line.isupper() and len(line) > 5):
                # Section headers
                p.setFont("Helvetica-Bold", 14)
                text = line.replace('##', '').strip()
                p.drawString(50, y_position, text)
                y_position -= 20

            elif line.startswith('•'):
                # Bullet points
                p.setFont("Helvetica", 9)
                bullet_text = line[1:].strip()

                # Wrap long bullet points
                wrapped_lines = textwrap.wrap(bullet_text, width=85)
                for i, wrapped_line in enumerate(wrapped_lines):
                    if i == 0:
                        p.drawString(70, y_position, f"• {wrapped_line}")
                    else:
                        p.drawString(80, y_position, wrapped_line)
                    y_position -= 12

                    # Check for new page
                    if y_position < 80:
                        p.showPage()
                        y_position = height - 50

            else:
                # Regular text (summary, descriptions)
                p.setFont("Helvetica", 10)

                # Wrap long lines
                wrapped_lines = textwrap.wrap(line, width=85)
                for wrapped_line in wrapped_lines:
                    p.drawString(50, y_position, wrapped_line)
                    y_position -= 14

                    # Check for new page
                    if y_position < 80:
                        p.showPage()
                        y_position = height - 50

        p.save()
        pdf_bytes = buffer.getvalue()
        buffer.close()
        return pdf_bytes

    except Exception as e:
        app.logger.error(f'PDF creation failed: {e}')
        # Create minimal fallback
        buffer = BytesIO()
        p = canvas.Canvas(buffer, pagesize=letter)
        p.setFont("Helvetica", 12)
        p.drawString(50, 750, "Alex Guyenne - Resume")
        p.drawString(50, 720, "PDF generation encountered an error.")
        p.drawString(50, 700, "Please copy the text version instead.")
        p.save()
        return buffer.getvalue()

if __name__ == '__main__':
    # Development server
    app.run(debug=True, host='0.0.0.0', port=8080)
else:
    # Production server (when run with gunicorn)
    import logging
    gunicorn_logger = logging.getLogger('gunicorn.error')
    app.logger.handlers = gunicorn_logger.handlers
    app.logger.setLevel(gunicorn_logger.level)