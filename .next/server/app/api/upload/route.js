"use strict";(()=>{var e={};e.id=998,e.ids=[998],e.modules={517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6953:(e,o,t)=>{t.r(o),t.d(o,{headerHooks:()=>l,originalPathname:()=>c,requestAsyncStorage:()=>n,routeModule:()=>i,serverHooks:()=>u,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>d});var r={};t.r(r),t.d(r,{POST:()=>POST});var a=t(884),s=t(6132);async function POST(e){try{let o=await e.formData(),t=o.get("file");if(!t)return Response.json({error:"No file provided"},{status:400});let r=new FormData;r.append("file",t);let a=await fetch("http://localhost:8080/upload",{method:"POST",body:r});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to process file")}let s=await a.json();return Response.json(s)}catch(e){return console.error("Upload error:",e),Response.json({error:e.message||"Failed to process file"},{status:500})}}let i=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"/Users/<USER>/Documents/augment-projects/Resume custom/app/api/upload/route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:n,staticGenerationAsyncStorage:p,serverHooks:u,headerHooks:l,staticGenerationBailout:d}=i,c="/api/upload/route"}};var o=require("../../../webpack-runtime.js");o.C(e);var __webpack_exec__=e=>o(o.s=e),t=o.X(0,[729],()=>__webpack_exec__(6953));module.exports=t})();