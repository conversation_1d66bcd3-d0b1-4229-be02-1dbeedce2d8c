# 🚀 Resume Tailor - Deployment Guide for User Testing

## 📋 Quick Start

### **Option 1: One-Click Deployment (Recommended)**
```bash
./deploy.sh
```
Choose option 1 (Docker) for the easiest deployment.

### **Option 2: Manual Docker Deployment**
```bash
# Build and run with <PERSON>er Compose
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### **Option 3: Local Development**
```bash
# Install dependencies
pip install -r requirements.txt
npm install

# Build frontend
npm run build

# Start backend
cd web_app && python3 app.py
```

---

## 🔧 Prerequisites

### **Required Software**
- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local deployment)
- **Node.js 18+** (for local deployment)

### **API Configuration**
1. **Get Together.ai API Key**: Sign up at [together.ai](https://together.ai)
2. **Update .env file**:
   ```env
   TOGETHER_API_KEY=your_actual_api_key_here
   TOGETHER_MODEL=meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo
   FLASK_ENV=production
   FLASK_DEBUG=False
   ```

---

## 🌐 Access Points

Once deployed, the application will be available at:

- **Main Application**: `http://localhost:8080`
- **API Health Check**: `http://localhost:8080/`
- **API Documentation**: See endpoints below

### **API Endpoints**
- `POST /generate` - Generate tailored resume
- `POST /upload` - Upload PDF/text files
- `POST /debug-parse` - Debug resume parsing

---

## 🧪 User Testing Instructions

### **For Test Users**

1. **Access the Application**
   - Open `http://localhost:8080` in your browser
   - You'll see the Resume Tailor interface

2. **Upload Your Resume**
   - Click "Upload Resume" button
   - Select a PDF or text file (max 10MB)
   - Wait for processing confirmation

3. **Paste Job Description**
   - Copy a job posting from LinkedIn, Indeed, etc.
   - Paste into the "Job Description" text area

4. **Add Key Achievements** (Optional)
   - List 1-3 specific accomplishments
   - One per line

5. **Generate Tailored Resume**
   - Click "Generate Tailored Resume"
   - Wait 10-30 seconds for AI processing
   - Review the enhanced resume and insights

6. **Provide Feedback**
   - Test different job descriptions
   - Try various resume formats
   - Note any issues or suggestions

### **Test Scenarios**
1. **Tech Roles**: Software Engineer, Data Scientist, DevOps
2. **Business Roles**: Product Manager, Sales, Marketing
3. **Different Industries**: Startup, Enterprise, Healthcare
4. **Various Experience Levels**: Entry-level, Mid-level, Senior

---

## 📊 Monitoring & Logs

### **Check Application Status**
```bash
# Docker deployment
docker-compose ps
curl http://localhost:8080/

# Local deployment
curl http://localhost:8080/
```

### **View Logs**
```bash
# Docker logs
docker-compose logs -f resume-tailor

# Local logs
# Check terminal output where app.py is running
```

### **Performance Monitoring**
- **Response Times**: API calls should complete in 10-30 seconds
- **Memory Usage**: Monitor with `docker stats` or system monitor
- **Error Rates**: Check logs for any 500 errors

---

## 🔒 Security Considerations

### **For Testing Environment**
- ✅ Rate limiting enabled (10 requests/minute per IP)
- ✅ File size limits (10MB max)
- ✅ CORS configured for frontend
- ✅ Input validation on all endpoints

### **For Production Deployment**
- 🔐 Add HTTPS/SSL certificates
- 🔐 Set up proper authentication
- 🔐 Configure firewall rules
- 🔐 Use environment-specific API keys

---

## 🐛 Troubleshooting

### **Common Issues**

1. **"LLM Service disabled"**
   - Check Together.ai API key in .env file
   - Verify API key is valid and has credits
   - Restart the application

2. **"File upload failed"**
   - Check file size (max 10MB)
   - Ensure file is PDF or text format
   - Try a different file

3. **"Application won't start"**
   - Check if port 8080 is available
   - Verify all dependencies are installed
   - Check Docker is running (for Docker deployment)

4. **"Slow response times"**
   - Together.ai API can take 10-30 seconds
   - Check internet connection
   - Monitor API rate limits

### **Getting Help**
```bash
# Check application logs
docker-compose logs resume-tailor

# Test API directly
curl -X POST http://localhost:8080/generate \
  -H "Content-Type: application/json" \
  -d '{"job_description":"test","base_resume_content":"test"}'

# Restart application
docker-compose restart
```

---

## 📈 Collecting User Feedback

### **What to Track**
- ✅ **Usability**: How easy is the interface?
- ✅ **Quality**: Are the AI enhancements helpful?
- ✅ **Performance**: Are response times acceptable?
- ✅ **Features**: What's missing or could be improved?

### **Feedback Collection Methods**
1. **Direct User Interviews**: 15-30 minute sessions
2. **Survey Forms**: Google Forms or Typeform
3. **Usage Analytics**: Monitor API endpoint usage
4. **Error Tracking**: Log and analyze any failures

### **Key Questions for Users**
1. How would you rate the quality of the tailored resume?
2. Did the AI insights help you understand job alignment?
3. What features would you like to see added?
4. Would you use this for your actual job applications?
5. How does this compare to other resume tools?

---

## 🚀 Next Steps After Testing

1. **Analyze Feedback**: Identify top improvement areas
2. **Prioritize Features**: Focus on high-impact Phase 2 features
3. **Scale Infrastructure**: Prepare for more users
4. **Implement Analytics**: Add user behavior tracking
5. **Plan Production**: Set up proper hosting and monitoring

---

## 📞 Support

For deployment issues or questions:
- Check the troubleshooting section above
- Review application logs
- Test with the provided curl commands
- Verify environment configuration
