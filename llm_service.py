"""
LLM Service for Resume Enhancement using Together.ai
"""

import os
import json
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from together import Together
except ImportError:
    print("Warning: together package not installed. LLM features will be disabled.")
    Together = None

class LLMService:
    def __init__(self):
        self.api_key = os.getenv('TOGETHER_API_KEY')
        self.model = os.getenv('TOGETHER_MODEL', 'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo')
        self.fallback_model = os.getenv('TOGETHER_FALLBACK_MODEL', 'mistralai/Mixtral-8x7B-Instruct-v0.1')

        if Together and self.api_key and self.api_key != 'your_together_ai_api_key_here':
            self.client = Together(api_key=self.api_key)
            self.enabled = True
        else:
            self.enabled = False
            self.client = None
            print("LLM Service disabled: Missing API key or together package")
    
    def is_enabled(self) -> bool:
        """Check if LLM service is properly configured and enabled"""
        return self.enabled
    
    def _make_request(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.7) -> Optional[str]:
        """Make a request to Together.ai API using new v1 format"""
        if not self.enabled or not self.client:
            return None

        try:
            # Convert prompt to messages format for chat completions
            messages = [{"role": "user", "content": prompt}]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                stop=["</s>", "[INST]", "[/INST]"]
            )

            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"Error with primary model {self.model}: {e}")
            # Try fallback model
            try:
                messages = [{"role": "user", "content": prompt}]
                response = self.client.chat.completions.create(
                    model=self.fallback_model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.9,
                    stop=["</s>", "[INST]", "[/INST]"]
                )

                if response and response.choices and len(response.choices) > 0:
                    return response.choices[0].message.content.strip()

            except Exception as fallback_error:
                print(f"Error with fallback model {self.fallback_model}: {fallback_error}")

        return None
    
    def enhance_summary(self, original_summary: str, job_description: str, key_achievements: List[str] = None) -> str:
        """Enhance professional summary using LLM"""
        if not self.enabled:
            return original_summary
        
        achievements_text = ""
        if key_achievements:
            achievements_text = f"\nKey Achievements: {', '.join(key_achievements)}"
        
        prompt = f"""[INST] You are a professional resume writer. Enhance this professional summary to better align with the job description while maintaining 100% authenticity.

Job Description: {job_description[:500]}...

Current Summary: {original_summary}
{achievements_text}

CRITICAL REQUIREMENTS:
- NEVER add fake experiences, skills, or achievements
- ONLY rephrase and optimize existing content
- Use industry terminology from the job description
- Keep the same core experiences and background
- Maximum 3-4 sentences
- Professional tone
- Return ONLY the enhanced summary, no explanations

Enhanced Summary: [/INST]"""
        
        enhanced = self._make_request(prompt, max_tokens=300, temperature=0.6)
        if enhanced:
            # Clean up the response - remove any explanatory text
            cleaned = self._clean_summary_response(enhanced)
            return cleaned if cleaned else original_summary
        return original_summary
    
    def enhance_bullet_points(self, bullet_points: List[str], job_description: str, job_title: str) -> List[str]:
        """Enhance work experience bullet points using LLM"""
        if not self.enabled or not bullet_points:
            return bullet_points
        
        bullets_text = "\n".join([f"• {bullet}" for bullet in bullet_points])
        
        prompt = f"""[INST] You are a professional resume writer. Enhance these work experience bullet points while maintaining 100% authenticity.

Target Role: {job_title}
Job Requirements: {job_description[:400]}...

Original Bullet Points:
{bullets_text}

CRITICAL REQUIREMENTS:
- NEVER add fake experiences, metrics, or achievements
- ONLY rephrase existing content with stronger action verbs
- Use terminology from the job description where relevant
- Keep the same core activities and accomplishments
- Each bullet should be 1-2 lines maximum
- Start each with a strong action verb
- Return ONLY the bullet points, one per line, starting with "•"
- NO explanations or additional text

Enhanced Bullet Points: [/INST]"""
        
        enhanced = self._make_request(prompt, max_tokens=500, temperature=0.7)
        
        if enhanced:
            # Parse the enhanced bullets back into a list
            enhanced_bullets = []
            lines = enhanced.split('\n')

            for line in lines:
                line = line.strip()
                # Skip empty lines and explanatory text
                if not line or line.lower().startswith(('here are', 'enhanced', 'bullet', 'points', 'i made', 'these', 'however')):
                    continue

                # Extract bullet points
                if line.startswith('•'):
                    bullet_text = line.lstrip('• ').strip()
                    if bullet_text and len(bullet_text) > 10:  # Ensure it's substantial content
                        enhanced_bullets.append(bullet_text)
                elif line.startswith('-'):
                    bullet_text = line.lstrip('- ').strip()
                    if bullet_text and len(bullet_text) > 10:
                        enhanced_bullets.append(bullet_text)
                elif line.startswith(('1.', '2.', '3.', '4.', '5.')):
                    bullet_text = line.split('.', 1)[1].strip()
                    if bullet_text and len(bullet_text) > 10:
                        enhanced_bullets.append(bullet_text)

            # If we got good bullets, return them; otherwise return originals
            return enhanced_bullets if enhanced_bullets else bullet_points
        
        return bullet_points
    
    def generate_strategic_insights(self, resume_content: str, job_description: str) -> Dict[str, str]:
        """Generate strategic insights for resume positioning"""
        if not self.enabled:
            return {
                "alignment_score": "N/A - LLM service disabled",
                "key_strengths": "Please configure Together.ai API key",
                "recommendations": "LLM enhancement unavailable",
                "missing_keywords": "API key required"
            }
        
        prompt = f"""[INST] You are a career strategist. Analyze this candidate's background against the job requirements and provide strategic insights.

Job Description: {job_description[:600]}...

Candidate Background: {resume_content[:800]}...

Provide analysis in this exact format:

ALIGNMENT_SCORE: [Score 1-10 with brief explanation]

KEY_STRENGTHS: [Top 3 strengths that align with the role]

RECOMMENDATIONS: [3 specific recommendations for positioning]

MISSING_KEYWORDS: [Important keywords from job description that should be emphasized]

[/INST]"""
        
        response = self._make_request(prompt, max_tokens=400, temperature=0.6)
        
        if response:
            # Parse the structured response
            insights = {
                "alignment_score": "8/10",
                "key_strengths": "Strong technical background",
                "recommendations": "Emphasize leadership experience",
                "missing_keywords": "Review job description"
            }
            
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('ALIGNMENT_SCORE:'):
                    insights["alignment_score"] = line.replace('ALIGNMENT_SCORE:', '').strip()
                elif line.startswith('KEY_STRENGTHS:'):
                    insights["key_strengths"] = line.replace('KEY_STRENGTHS:', '').strip()
                elif line.startswith('RECOMMENDATIONS:'):
                    insights["recommendations"] = line.replace('RECOMMENDATIONS:', '').strip()
                elif line.startswith('MISSING_KEYWORDS:'):
                    insights["missing_keywords"] = line.replace('MISSING_KEYWORDS:', '').strip()
            
            return insights
        
        return {
            "alignment_score": "Unable to analyze",
            "key_strengths": "LLM service unavailable",
            "recommendations": "Please check API configuration",
            "missing_keywords": "Service error"
        }

    def _clean_summary_response(self, response: str) -> str:
        """Clean up LLM response for summary enhancement"""
        lines = response.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            # Skip explanatory text and meta-commentary
            if line.lower().startswith(('here is', 'enhanced', 'summary', 'i have', 'this', 'the enhanced')):
                continue
            if line and len(line) > 20:  # Ensure substantial content
                cleaned_lines.append(line)

        # Return the first substantial paragraph
        if cleaned_lines:
            return cleaned_lines[0]
        return response.strip()

# Global instance
llm_service = LLMService()
