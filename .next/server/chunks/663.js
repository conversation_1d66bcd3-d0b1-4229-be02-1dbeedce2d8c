exports.id=663,exports.ids=[663],exports.modules={4635:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,isArray=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},isPlainObject=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},setProperty=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},getProperty=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function extend(){var e,t,n,r,i,o,a=arguments[0],l=1,s=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[1]||{},l=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});l<s;++l)if(e=arguments[l],null!=e)for(t in e)n=getProperty(a,t),a!==(r=getProperty(e,t))&&(c&&r&&(isPlainObject(r)||(i=isArray(r)))?(i?(i=!1,o=n&&isArray(n)?n:[]):o=n&&isPlainObject(n)?n:{},setProperty(a,{name:t,newValue:extend(c,o,r)})):void 0!==r&&setProperty(a,{name:t,newValue:r}));return a}},1186:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,l=/^[;\s]*/,s=/^\s+|\s+$/g;function trim(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var c=1,u=1;function updatePosition(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");u=~r?e.length-r:u+e.length}function position(){var e={line:c,column:u};return function(t){return t.position=new Position(e),match(r),t}}function Position(e){this.start=e,this.end={line:c,column:u},this.source=s.source}Position.prototype.content=e;var p=[];function error(t){var n=Error(s.source+":"+c+":"+u+": "+t);if(n.reason=t,n.filename=s.source,n.line=c,n.column=u,n.source=e,s.silent)p.push(n);else throw n}function match(t){var n=t.exec(e);if(n){var r=n[0];return updatePosition(r),e=e.slice(r.length),n}}function comments(e){var t;for(e=e||[];t=comment();)!1!==t&&e.push(t);return e}function comment(){var t=position();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return error("End of comment missing");var r=e.slice(2,n-2);return u+=2,updatePosition(r),e=e.slice(n),u+=2,t({type:"comment",comment:r})}}function declaration(){var e=position(),n=match(i);if(n){if(comment(),!match(o))return error("property missing ':'");var r=match(a),s=e({type:"declaration",property:trim(n[0].replace(t,"")),value:r?trim(r[0].replace(t,"")):""});return match(l),s}}function declarations(){var e,t=[];for(comments(t);e=declaration();)!1!==e&&(t.push(e),comments(t));return t}return match(r),declarations()}},784:(e,t,n)=>{"use strict";e.exports=n(316).vendored["react-ssr"].ReactJsxRuntime},3791:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(2209)),i=n(8670);function StyleToJS(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}StyleToJS.default=StyleToJS,e.exports=StyleToJS},8670:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,a=/^-(ms)-/,capitalize=function(e,t){return t.toUpperCase()},trimHyphen=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var l;return(void 0===t&&(t={}),!(l=e)||i.test(l)||n.test(l))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(a,trimHyphen):e.replace(o,trimHyphen)).replace(r,capitalize))}},2209:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=StyleToObject;var i=r(n(1186));function StyleToObject(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n}},663:(e,t,n)=>{"use strict";n.d(t,{UG:()=>Markdown});var r={};n.r(r),n.d(r,{boolean:()=>u,booleanish:()=>p,commaOrSpaceSeparated:()=>g,commaSeparated:()=>m,number:()=>f,overloadedBoolean:()=>d,spaceSeparated:()=>h});var i={};function stringify(e,t){let n=t||{},r=""===e[e.length-1]?[...e,""]:e;return r.join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}n.r(i),n.d(i,{attentionMarkers:()=>eV,contentInitial:()=>eN,disable:()=>eU,document:()=>eO,flow:()=>eF,flowInitial:()=>eB,insideSpan:()=>eM,string:()=>eR,text:()=>e_});let o=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,a=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,l={};function lib_name(e,t){let n=(t||l).jsx?a:o;return n.test(e)}let s=/[ \t\n\f\r]/g;function whitespace(e){return"object"==typeof e?"text"===e.type&&empty(e.value):empty(e)}function empty(e){return""===e.replace(s,"")}let Schema=class Schema{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}};function merge(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new Schema(n,r,t)}function normalize(e){return e.toLowerCase()}Schema.prototype.normal={},Schema.prototype.property={},Schema.prototype.space=void 0;let Info=class Info{constructor(e,t){this.attribute=t,this.property=e}};Info.prototype.attribute="",Info.prototype.booleanish=!1,Info.prototype.boolean=!1,Info.prototype.commaOrSpaceSeparated=!1,Info.prototype.commaSeparated=!1,Info.prototype.defined=!1,Info.prototype.mustUseProperty=!1,Info.prototype.number=!1,Info.prototype.overloadedBoolean=!1,Info.prototype.property="",Info.prototype.spaceSeparated=!1,Info.prototype.space=void 0;let c=0,u=increment(),p=increment(),d=increment(),f=increment(),h=increment(),m=increment(),g=increment();function increment(){return 2**++c}let y=Object.keys(r);let DefinedInfo=class DefinedInfo extends Info{constructor(e,t,n,i){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",i),"number"==typeof n)for(;++o<y.length;){let e=y[o];(function(e,t,n){n&&(e[t]=n)})(this,y[o],(n&r[e])===r[e])}}};function create(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let o=new DefinedInfo(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[normalize(r)]=r,n[normalize(o.attribute)]=r}return new Schema(t,n,e.space)}DefinedInfo.prototype.defined=!0;let k=create({properties:{ariaActiveDescendant:null,ariaAtomic:p,ariaAutoComplete:null,ariaBusy:p,ariaChecked:p,ariaColCount:f,ariaColIndex:f,ariaColSpan:f,ariaControls:h,ariaCurrent:null,ariaDescribedBy:h,ariaDetails:null,ariaDisabled:p,ariaDropEffect:h,ariaErrorMessage:null,ariaExpanded:p,ariaFlowTo:h,ariaGrabbed:p,ariaHasPopup:null,ariaHidden:p,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:h,ariaLevel:f,ariaLive:null,ariaModal:p,ariaMultiLine:p,ariaMultiSelectable:p,ariaOrientation:null,ariaOwns:h,ariaPlaceholder:null,ariaPosInSet:f,ariaPressed:p,ariaReadOnly:p,ariaRelevant:null,ariaRequired:p,ariaRoleDescription:h,ariaRowCount:f,ariaRowIndex:f,ariaRowSpan:f,ariaSelected:p,ariaSetSize:f,ariaSort:null,ariaValueMax:f,ariaValueMin:f,ariaValueNow:f,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function caseSensitiveTransform(e,t){return t in e?e[t]:t}function caseInsensitiveTransform(e,t){return caseSensitiveTransform(e,t.toLowerCase())}let x=create({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:m,acceptCharset:h,accessKey:h,action:null,allow:null,allowFullScreen:u,allowPaymentRequest:u,allowUserMedia:u,alt:null,as:null,async:u,autoCapitalize:null,autoComplete:h,autoFocus:u,autoPlay:u,blocking:h,capture:null,charSet:null,checked:u,cite:null,className:h,cols:f,colSpan:null,content:null,contentEditable:p,controls:u,controlsList:h,coords:f|m,crossOrigin:null,data:null,dateTime:null,decoding:null,default:u,defer:u,dir:null,dirName:null,disabled:u,download:d,draggable:p,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:u,formTarget:null,headers:h,height:f,hidden:d,high:f,href:null,hrefLang:null,htmlFor:h,httpEquiv:h,id:null,imageSizes:null,imageSrcSet:null,inert:u,inputMode:null,integrity:null,is:null,isMap:u,itemId:null,itemProp:h,itemRef:h,itemScope:u,itemType:h,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:u,low:f,manifest:null,max:null,maxLength:f,media:null,method:null,min:null,minLength:f,multiple:u,muted:u,name:null,nonce:null,noModule:u,noValidate:u,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:u,optimum:f,pattern:null,ping:h,placeholder:null,playsInline:u,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:u,referrerPolicy:null,rel:h,required:u,reversed:u,rows:f,rowSpan:f,sandbox:h,scope:null,scoped:u,seamless:u,selected:u,shadowRootClonable:u,shadowRootDelegatesFocus:u,shadowRootMode:null,shape:null,size:f,sizes:null,slot:null,span:f,spellCheck:p,src:null,srcDoc:null,srcLang:null,srcSet:null,start:f,step:null,style:null,tabIndex:f,target:null,title:null,translate:null,type:null,typeMustMatch:u,useMap:null,value:p,width:f,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:h,axis:null,background:null,bgColor:null,border:f,borderColor:null,bottomMargin:f,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:u,declare:u,event:null,face:null,frame:null,frameBorder:null,hSpace:f,leftMargin:f,link:null,longDesc:null,lowSrc:null,marginHeight:f,marginWidth:f,noResize:u,noHref:u,noShade:u,noWrap:u,object:null,profile:null,prompt:null,rev:null,rightMargin:f,rules:null,scheme:null,scrolling:p,standby:null,summary:null,text:null,topMargin:f,valueType:null,version:null,vAlign:null,vLink:null,vSpace:f,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:u,disableRemotePlayback:u,prefix:null,property:null,results:f,security:null,unselectable:null},space:"html",transform:caseInsensitiveTransform}),b=create({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:g,accentHeight:f,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:f,amplitude:f,arabicForm:null,ascent:f,attributeName:null,attributeType:null,azimuth:f,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:f,by:null,calcMode:null,capHeight:f,className:h,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:f,diffuseConstant:f,direction:null,display:null,dur:null,divisor:f,dominantBaseline:null,download:u,dx:null,dy:null,edgeMode:null,editable:null,elevation:f,enableBackground:null,end:null,event:null,exponent:f,externalResourcesRequired:null,fill:null,fillOpacity:f,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:m,g2:m,glyphName:m,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:f,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:f,horizOriginX:f,horizOriginY:f,id:null,ideographic:f,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:f,k:f,k1:f,k2:f,k3:f,k4:f,kernelMatrix:g,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:f,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:f,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:f,overlineThickness:f,paintOrder:null,panose1:null,path:null,pathLength:f,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:h,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:f,pointsAtY:f,pointsAtZ:f,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:g,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:g,rev:g,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:g,requiredFeatures:g,requiredFonts:g,requiredFormats:g,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:f,specularExponent:f,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:f,strikethroughThickness:f,string:null,stroke:null,strokeDashArray:g,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:f,strokeOpacity:f,strokeWidth:null,style:null,surfaceScale:f,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:g,tabIndex:f,tableValues:null,target:null,targetX:f,targetY:f,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:g,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:f,underlineThickness:f,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:f,values:null,vAlphabetic:f,vMathematical:f,vectorEffect:null,vHanging:f,vIdeographic:f,version:null,vertAdvY:f,vertOriginX:f,vertOriginY:f,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:f,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:caseSensitiveTransform}),v=create({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),w=create({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:caseInsensitiveTransform}),S=create({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),E=merge([k,x,v,w,S],"html"),C=merge([k,b,v,w,S],"svg"),A=/[A-Z]/g,L=/-[a-z]/g,I=/^data[-\w.:]+$/i;function find(e,t){let n=normalize(t),r=t,i=Info;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&I.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(L,camelcase);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!L.test(e)){let n=e.replace(A,kebab);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}i=DefinedInfo}return new i(r,t)}function kebab(e){return"-"+e.toLowerCase()}function camelcase(e){return e.charAt(1).toUpperCase()}let T={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};function space_separated_tokens_stringify(e){return e.join(" ").trim()}var z=n(3791);let P=point("end"),D=point("start");function point(e){return point;function point(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function position(e){let t=D(e),n=P(e);if(t&&n)return{start:t,end:n}}function stringifyPosition(e){return e&&"object"==typeof e?"position"in e||"type"in e?lib_position(e.position):"start"in e||"end"in e?lib_position(e):"line"in e||"column"in e?lib_point(e):"":""}function lib_point(e){return index(e&&e.line)+":"+index(e&&e.column)}function lib_position(e){return lib_point(e&&e.start)+"-"+lib_point(e&&e.end)}function index(e){return e&&"number"==typeof e?e:1}let VFileMessage=class VFileMessage extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},o=!1;if(t&&(i="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(o=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let a=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=a?a.line:void 0,this.name=stringifyPosition(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=o&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}};VFileMessage.prototype.file="",VFileMessage.prototype.name="",VFileMessage.prototype.reason="",VFileMessage.prototype.message="",VFileMessage.prototype.stack="",VFileMessage.prototype.column=void 0,VFileMessage.prototype.line=void 0,VFileMessage.prototype.ancestors=void 0,VFileMessage.prototype.cause=void 0,VFileMessage.prototype.fatal=void 0,VFileMessage.prototype.place=void 0,VFileMessage.prototype.ruleId=void 0,VFileMessage.prototype.source=void 0;let q={}.hasOwnProperty,O=new Map,N=/[A-Z]/g,B=new Set(["table","tbody","thead","tfoot","tr"]),F=new Set(["td","th"]),R="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function toJsxRuntime(e,t){let n;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let r=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=developmentCreate(r,t.jsxDEV)}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");n=productionCreate(r,t.jsx,t.jsxs)}let i={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:n,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:r,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?C:E,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},o=one(i,e,void 0);return o&&"string"!=typeof o?o:i.create(e,i.Fragment,{children:o||void 0},void 0)}function one(e,t,n){return"element"===t.type?lib_element(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?mdxExpression(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?mdxJsxElement(e,t,n):"mdxjsEsm"===t.type?mdxEsm(e,t):"root"===t.type?root(e,t,n):"text"===t.type?lib_text(e,t):void 0}function lib_element(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=C,e.schema=i),e.ancestors.push(t);let o=findComponentFromName(e,t.tagName,!1),a=createElementProps(e,t),l=createChildren(e,t);return B.has(t.tagName)&&(l=l.filter(function(e){return"string"!=typeof e||!whitespace(e)})),addNode(e,a,o,t),addChildren(a,l),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}function mdxExpression(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree,r=n.body[0];return r.type,e.evaluater.evaluateExpression(r.expression)}crashEstree(e,t.position)}function mdxEsm(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);crashEstree(e,t.position)}function mdxJsxElement(e,t,n){let r=e.schema,i=r;"svg"===t.name&&"html"===r.space&&(i=C,e.schema=i),e.ancestors.push(t);let o=null===t.name?e.Fragment:findComponentFromName(e,t.name,!0),a=createJsxElementProps(e,t),l=createChildren(e,t);return addNode(e,a,o,t),addChildren(a,l),e.ancestors.pop(),e.schema=r,e.create(t,o,a,n)}function root(e,t,n){let r={};return addChildren(r,createChildren(e,t)),e.create(t,e.Fragment,r,n)}function lib_text(e,t){return t.value}function addNode(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function addChildren(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function productionCreate(e,t,n){return create;function create(e,r,i,o){let a=Array.isArray(i.children),l=a?n:t;return o?l(r,i,o):l(r,i)}}function developmentCreate(e,t){return create;function create(n,r,i,o){let a=Array.isArray(i.children),l=D(n);return t(r,i,o,a,{columnNumber:l?l.column-1:void 0,fileName:e,lineNumber:l?l.line:void 0},void 0)}}function createElementProps(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&q.call(t.properties,r)){let o=createProperty(e,r,t.properties[r]);if(o){let[r,a]=o;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof a&&F.has(t.tagName)?n=a:i[r]=a}}if(n){let t=i.style||(i.style={});t["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n}return i}function createJsxElementProps(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree,i=t.body[0];i.type;let o=i.expression;o.type;let a=o.properties[0];a.type,Object.assign(n,e.evaluater.evaluateExpression(a.argument))}else crashEstree(e,t.position)}else{let i;let o=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree,n=t.body[0];n.type,i=e.evaluater.evaluateExpression(n.expression)}else crashEstree(e,t.position)}else i=null===r.value||r.value;n[o]=i}return n}function createChildren(e,t){let n=[],r=-1,i=e.passKeys?new Map:O;for(;++r<t.children.length;){let o;let a=t.children[r];if(e.passKeys){let e="element"===a.type?a.tagName:"mdxJsxFlowElement"===a.type||"mdxJsxTextElement"===a.type?a.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let l=one(e,a,o);void 0!==l&&n.push(l)}return n}function createProperty(e,t,n){let r=find(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?stringify(n):space_separated_tokens_stringify(n)),"style"===r.property){let t="object"==typeof n?n:parseStyle(e,String(n));return"css"===e.stylePropertyNameCase&&(t=transformStylesToCssCasing(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?T[r.property]||r.property:r.attribute,n]}}function parseStyle(e,t){try{return z(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new VFileMessage("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=R+"#cannot-parse-style-attribute",t}}function findComponentFromName(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),i=-1;for(;++i<n.length;){let t=lib_name(n[i])?{type:"Identifier",name:n[i]}:{type:"Literal",value:n[i]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(i&&"Literal"===t.type),optional:!1}:t}r=e}else r=lib_name(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return q.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);crashEstree(e)}function crashEstree(e,t){let n=new VFileMessage("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=R+"#cannot-handle-mdx-estrees-without-createevaluater",n}function transformStylesToCssCasing(e){let t;let n={};for(t in e)q.call(e,t)&&(n[transformStyleToCssCasing(t)]=e[t]);return n}function transformStyleToCssCasing(e){let t=e.replace(N,toDash);return"ms-"===t.slice(0,3)&&(t="-"+t),t}function toDash(e){return"-"+e.toLowerCase()}let _={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var M=n(784);n(9885);let V={};function lib_toString(e,t){let n=t||V,r="boolean"!=typeof n.includeImageAlt||n.includeImageAlt,i="boolean"!=typeof n.includeHtml||n.includeHtml;return lib_one(e,r,i)}function lib_one(e,t,n){if(node(e)){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return lib_all(e.children,t,n)}return Array.isArray(e)?lib_all(e,t,n):""}function lib_all(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=lib_one(e[i],t,n);return r.join("")}function node(e){return!!(e&&"object"==typeof e)}function splice(e,t,n,r){let i;let o=e.length,a=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)(i=r.slice(a,a+1e4)).unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function push(e,t){return e.length>0?(splice(e,e.length,0,t),e):t}let SpliceBuffer=class SpliceBuffer{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){let r=t||0;this.setCursor(Math.trunc(e));let i=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&chunkedPush(this.left,n),i.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),chunkedPush(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),chunkedPush(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);chunkedPush(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);chunkedPush(this.left,t.reverse())}}}};function chunkedPush(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function subtokenize(e){let t,n,r,i,o,a,l;let s={},c=-1,u=new SpliceBuffer(e);for(;++c<u.length;){for(;(c in s);)c=s[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(a=t[1]._tokenizer.events).length&&"lineEndingBlank"===a[r][1].type&&(r+=2),r<a.length&&"content"===a[r][1].type))for(;++r<a.length&&"content"!==a[r][1].type;)"chunkText"===a[r][1].type&&(a[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,subcontent(u,c)),c=s[c],l=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(i=u.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...u.get(n)[1].start},(o=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,o))}}return splice(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!l}function subcontent(e,t){let n,r;let i=e.get(t)[1],o=e.get(t)[2],a=t-1,l=[],s=i._tokenizer;!s&&(s=o.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let c=s.events,u=[],p={},d=-1,f=i,h=0,m=0,g=[m];for(;f;){for(;e.get(++a)[1]!==f;);l.push(a),!f._tokenizer&&(n=o.sliceStream(f),f.next||n.push(null),r&&s.defineSkip(f.start),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),f._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=i;++d<c.length;)"exit"===c[d][0]&&"enter"===c[d-1][0]&&c[d][1].type===c[d-1][1].type&&c[d][1].start.line!==c[d][1].end.line&&(m=d+1,g.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(s.events=[],f?(f._tokenizer=void 0,f.previous=void 0):g.pop(),d=g.length;d--;){let t=c.slice(g[d],g[d+1]),n=l.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),d=-1;++d<u.length;)p[h+u[d][0]]=h+u[d][1],h+=u[d][1]-u[d][0]-1;return p}function postprocess(e){for(;!subtokenize(e););return e}let U={}.hasOwnProperty;function combineExtensions(e){let t={},n=-1;for(;++n<e.length;)syntaxExtension(t,e[n]);return t}function syntaxExtension(e,t){let n;for(n in t){let r;let i=U.call(e,n)?e[n]:void 0,o=i||(e[n]={}),a=t[n];if(a)for(r in a){U.call(o,r)||(o[r]=[]);let e=a[r];constructs(o[r],Array.isArray(e)?e:e?[e]:[])}}}function constructs(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);splice(e,0,0,r)}let H=regexCheck(/[A-Za-z]/),j=regexCheck(/[\dA-Za-z]/),G=regexCheck(/[#-'*+\--9=?A-Z^-~]/);function asciiControl(e){return null!==e&&(e<32||127===e)}let Q=regexCheck(/\d/),W=regexCheck(/[\dA-Fa-f]/),J=regexCheck(/[!-/:-@[-`{-~]/);function markdownLineEnding(e){return null!==e&&e<-2}function markdownLineEndingOrSpace(e){return null!==e&&(e<0||32===e)}function markdownSpace(e){return -2===e||-1===e||32===e}let Y=regexCheck(/\p{P}|\p{S}/u),K=regexCheck(/\s/);function regexCheck(e){return check;function check(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function factorySpace(e,t,n,r){let i=r?r-1:Number.POSITIVE_INFINITY,o=0;return start;function start(r){return markdownSpace(r)?(e.enter(n),prefix(r)):t(r)}function prefix(r){return markdownSpace(r)&&o++<i?(e.consume(r),prefix):(e.exit(n),t(r))}}let Z={tokenize:initializeContent};function initializeContent(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,afterContentStartConstruct,paragraphInitial);return n;function afterContentStartConstruct(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),factorySpace(e,n,"linePrefix")}function paragraphInitial(t){return e.enter("paragraph"),lineStart(t)}function lineStart(n){let r=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=r),t=r,data(n)}function data(t){if(null===t){e.exit("chunkText"),e.exit("paragraph"),e.consume(t);return}return markdownLineEnding(t)?(e.consume(t),e.exit("chunkText"),lineStart):(e.consume(t),data)}}let X={tokenize:initializeDocument},$={tokenize:tokenizeContainer};function initializeDocument(e){let t,n,r;let i=this,o=[],a=0;return start;function start(t){if(a<o.length){let n=o[a];return i.containerState=n[1],e.attempt(n[0].continuation,documentContinue,checkNewContainers)(t)}return checkNewContainers(t)}function documentContinue(e){if(a++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&closeFlow();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}exitContainers(a);let l=r;for(;l<i.events.length;)i.events[l][1].end={...n},l++;return splice(i.events,o+1,0,i.events.slice(r)),i.events.length=l,checkNewContainers(e)}return start(e)}function checkNewContainers(n){if(a===o.length){if(!t)return documentContinued(n);if(t.currentConstruct&&t.currentConstruct.concrete)return flowStart(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check($,thereIsANewContainer,thereIsNoNewContainer)(n)}function thereIsANewContainer(e){return t&&closeFlow(),exitContainers(a),documentContinued(e)}function thereIsNoNewContainer(e){return i.parser.lazy[i.now().line]=a!==o.length,r=i.now().offset,flowStart(e)}function documentContinued(t){return i.containerState={},e.attempt($,containerContinue,flowStart)(t)}function containerContinue(e){return a++,o.push([i.currentConstruct,i.containerState]),documentContinued(e)}function flowStart(r){if(null===r){t&&closeFlow(),exitContainers(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),flowContinue(r)}function flowContinue(t){if(null===t){writeToChild(e.exit("chunkFlow"),!0),exitContainers(0),e.consume(t);return}return markdownLineEnding(t)?(e.consume(t),writeToChild(e.exit("chunkFlow")),a=0,i.interrupt=void 0,start):(e.consume(t),flowContinue)}function writeToChild(e,o){let l=i.sliceStream(e);if(o&&l.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(l),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let l=i.events.length,s=l;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(exitContainers(a),o=l;o<i.events.length;)i.events[o][1].end={...n},o++;splice(i.events,s+1,0,i.events.slice(l)),i.events.length=o}}function exitContainers(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function closeFlow(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}function tokenizeContainer(e,t,n){return factorySpace(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}let ee={partial:!0,tokenize:tokenizeBlankLine};function tokenizeBlankLine(e,t,n){return start;function start(t){return markdownSpace(t)?factorySpace(e,after,"linePrefix")(t):after(t)}function after(e){return null===e||markdownLineEnding(e)?t(e):n(e)}}let et={resolve:resolveContent,tokenize:tokenizeContent},en={partial:!0,tokenize:tokenizeContinuation};function resolveContent(e){return subtokenize(e),e}function tokenizeContent(e,t){let n;return chunkStart;function chunkStart(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),chunkInside(t)}function chunkInside(t){return null===t?contentEnd(t):markdownLineEnding(t)?e.check(en,contentContinue,contentEnd)(t):(e.consume(t),chunkInside)}function contentEnd(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function contentContinue(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,chunkInside}}function tokenizeContinuation(e,t,n){let r=this;return startLookahead;function startLookahead(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),factorySpace(e,prefixed,"linePrefix")}function prefixed(i){if(null===i||markdownLineEnding(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}let er={tokenize:initializeFlow};function initializeFlow(e){let t=this,n=e.attempt(ee,atBlankEnding,e.attempt(this.parser.constructs.flowInitial,afterConstruct,factorySpace(e,e.attempt(this.parser.constructs.flow,afterConstruct,e.attempt(et,afterConstruct)),"linePrefix")));return n;function atBlankEnding(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function afterConstruct(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}let ei={resolveAll:createResolver()},eo=initializeFactory("string"),ea=initializeFactory("text");function initializeFactory(e){return{resolveAll:createResolver("text"===e?resolveAllLineSuffixes:void 0),tokenize:initializeText};function initializeText(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,start,notText);return start;function start(e){return atBreak(e)?i(e):notText(e)}function notText(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),data}function data(e){return atBreak(e)?(t.exit("data"),i(e)):(t.consume(e),data)}function atBreak(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}function createResolver(e){return resolveAllText;function resolveAllText(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function resolveAllLineSuffixes(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],o=t.sliceStream(i),a=o.length,l=-1,s=0;for(;a--;){let e=o[a];if("string"==typeof e){for(l=e.length;32===e.charCodeAt(l-1);)s++,l--;if(l)break;l=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let o={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?l:i.start._bufferIndex+l,_index:i.start._index+a,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let el={name:"thematicBreak",tokenize:tokenizeThematicBreak};function tokenizeThematicBreak(e,t,n){let r,i=0;return start;function start(t){return e.enter("thematicBreak"),before(t)}function before(e){return r=e,atBreak(e)}function atBreak(o){return o===r?(e.enter("thematicBreakSequence"),sequence(o)):i>=3&&(null===o||markdownLineEnding(o))?(e.exit("thematicBreak"),t(o)):n(o)}function sequence(t){return t===r?(e.consume(t),i++,sequence):(e.exit("thematicBreakSequence"),markdownSpace(t)?factorySpace(e,atBreak,"whitespace")(t):atBreak(t))}}let es={continuation:{tokenize:tokenizeListContinuation},exit:tokenizeListEnd,name:"list",tokenize:tokenizeListStart},ec={partial:!0,tokenize:tokenizeListItemPrefixWhitespace},eu={partial:!0,tokenize:tokenizeIndent};function tokenizeListStart(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,a=0;return start;function start(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:Q(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(el,n,atMarker)(t):atMarker(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),inside(t)}return n(t)}function inside(t){return Q(t)&&++a<10?(e.consume(t),inside):(!r.interrupt||a<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),atMarker(t)):n(t)}function atMarker(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(ee,r.interrupt?n:onBlank,e.attempt(ec,endOfPrefix,otherPrefix))}function onBlank(e){return r.containerState.initialBlankLine=!0,o++,endOfPrefix(e)}function otherPrefix(t){return markdownSpace(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),endOfPrefix):n(t)}function endOfPrefix(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}function tokenizeListContinuation(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(ee,onBlank,notBlank);function onBlank(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,factorySpace(e,t,"listItemIndent",r.containerState.size+1)(n)}function notBlank(n){return r.containerState.furtherBlankLines||!markdownSpace(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,notInCurrentItem(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(eu,t,notInCurrentItem)(n))}function notInCurrentItem(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,factorySpace(e,e.attempt(es,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}function tokenizeIndent(e,t,n){let r=this;return factorySpace(e,afterPrefix,"listItemIndent",r.containerState.size+1);function afterPrefix(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)}}function tokenizeListEnd(e){e.exit(this.containerState.type)}function tokenizeListItemPrefixWhitespace(e,t,n){let r=this;return factorySpace(e,afterPrefix,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5);function afterPrefix(e){let i=r.events[r.events.length-1];return!markdownSpace(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)}}let ep={continuation:{tokenize:tokenizeBlockQuoteContinuation},exit,name:"blockQuote",tokenize:tokenizeBlockQuoteStart};function tokenizeBlockQuoteStart(e,t,n){let r=this;return start;function start(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),after}return n(t)}function after(n){return markdownSpace(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}function tokenizeBlockQuoteContinuation(e,t,n){let r=this;return contStart;function contStart(t){return markdownSpace(t)?factorySpace(e,contBefore,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):contBefore(t)}function contBefore(r){return e.attempt(ep,t,n)(r)}}function exit(e){e.exit("blockQuote")}function factoryDestination(e,t,n,r,i,o,a,l,s){let c=s||Number.POSITIVE_INFINITY,u=0;return start;function start(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),enclosedBefore):null===t||32===t||41===t||asciiControl(t)?n(t):(e.enter(r),e.enter(a),e.enter(l),e.enter("chunkString",{contentType:"string"}),raw(t))}function enclosedBefore(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(l),e.enter("chunkString",{contentType:"string"}),enclosed(n))}function enclosed(t){return 62===t?(e.exit("chunkString"),e.exit(l),enclosedBefore(t)):null===t||60===t||markdownLineEnding(t)?n(t):(e.consume(t),92===t?enclosedEscape:enclosed)}function enclosedEscape(t){return 60===t||62===t||92===t?(e.consume(t),enclosed):enclosed(t)}function raw(i){return!u&&(null===i||41===i||markdownLineEndingOrSpace(i))?(e.exit("chunkString"),e.exit(l),e.exit(a),e.exit(r),t(i)):u<c&&40===i?(e.consume(i),u++,raw):41===i?(e.consume(i),u--,raw):null===i||32===i||40===i||asciiControl(i)?n(i):(e.consume(i),92===i?rawEscape:raw)}function rawEscape(t){return 40===t||41===t||92===t?(e.consume(t),raw):raw(t)}}function factoryLabel(e,t,n,r,i,o){let a;let l=this,s=0;return start;function start(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),atBreak}function atBreak(c){return s>999||null===c||91===c||93===c&&!a||94===c&&!s&&"_hiddenFootnoteSupport"in l.parser.constructs?n(c):93===c?(e.exit(o),e.enter(i),e.consume(c),e.exit(i),e.exit(r),t):markdownLineEnding(c)?(e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),atBreak):(e.enter("chunkString",{contentType:"string"}),labelInside(c))}function labelInside(t){return null===t||91===t||93===t||markdownLineEnding(t)||s++>999?(e.exit("chunkString"),atBreak(t)):(e.consume(t),a||(a=!markdownSpace(t)),92===t?labelEscape:labelInside)}function labelEscape(t){return 91===t||92===t||93===t?(e.consume(t),s++,labelInside):labelInside(t)}}function factoryTitle(e,t,n,r,i,o){let a;return start;function start(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),a=40===t?41:t,begin):n(t)}function begin(n){return n===a?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),atBreak(n))}function atBreak(t){return t===a?(e.exit(o),begin(a)):null===t?n(t):markdownLineEnding(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),factorySpace(e,atBreak,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),inside(t))}function inside(t){return t===a||null===t||markdownLineEnding(t)?(e.exit("chunkString"),atBreak(t)):(e.consume(t),92===t?escape:inside)}function escape(t){return t===a||92===t?(e.consume(t),inside):inside(t)}}function factoryWhitespace(e,t){let n;return start;function start(r){return markdownLineEnding(r)?(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n=!0,start):markdownSpace(r)?factorySpace(e,start,n?"linePrefix":"lineSuffix")(r):t(r)}}function normalizeIdentifier(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let ed={partial:!0,tokenize:tokenizeTitleBefore};function tokenizeDefinition(e,t,n){let r;let i=this;return start;function start(t){return e.enter("definition"),before(t)}function before(t){return factoryLabel.call(i,e,labelAfter,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)}function labelAfter(t){return(r=normalizeIdentifier(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),markerAfter):n(t)}function markerAfter(t){return markdownLineEndingOrSpace(t)?factoryWhitespace(e,destinationBefore)(t):destinationBefore(t)}function destinationBefore(t){return factoryDestination(e,destinationAfter,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function destinationAfter(t){return e.attempt(ed,after,after)(t)}function after(t){return markdownSpace(t)?factorySpace(e,afterWhitespace,"whitespace")(t):afterWhitespace(t)}function afterWhitespace(o){return null===o||markdownLineEnding(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}function tokenizeTitleBefore(e,t,n){return titleBefore;function titleBefore(t){return markdownLineEndingOrSpace(t)?factoryWhitespace(e,beforeMarker)(t):n(t)}function beforeMarker(t){return factoryTitle(e,titleAfter,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function titleAfter(t){return markdownSpace(t)?factorySpace(e,titleAfterOptionalWhitespace,"whitespace")(t):titleAfterOptionalWhitespace(t)}function titleAfterOptionalWhitespace(e){return null===e||markdownLineEnding(e)?t(e):n(e)}}let ef={name:"codeIndented",tokenize:tokenizeCodeIndented},eh={partial:!0,tokenize:tokenizeFurtherStart};function tokenizeCodeIndented(e,t,n){let r=this;return start;function start(t){return e.enter("codeIndented"),factorySpace(e,afterPrefix,"linePrefix",5)(t)}function afterPrefix(e){let t=r.events[r.events.length-1];return t&&"linePrefix"===t[1].type&&t[2].sliceSerialize(t[1],!0).length>=4?atBreak(e):n(e)}function atBreak(t){return null===t?after(t):markdownLineEnding(t)?e.attempt(eh,atBreak,after)(t):(e.enter("codeFlowValue"),inside(t))}function inside(t){return null===t||markdownLineEnding(t)?(e.exit("codeFlowValue"),atBreak(t)):(e.consume(t),inside)}function after(n){return e.exit("codeIndented"),t(n)}}function tokenizeFurtherStart(e,t,n){let r=this;return furtherStart;function furtherStart(t){return r.parser.lazy[r.now().line]?n(t):markdownLineEnding(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),furtherStart):factorySpace(e,afterPrefix,"linePrefix",5)(t)}function afterPrefix(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):markdownLineEnding(e)?furtherStart(e):n(e)}}function resolveHeadingAtx(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},splice(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e}function tokenizeHeadingAtx(e,t,n){let r=0;return start;function start(t){return e.enter("atxHeading"),before(t)}function before(t){return e.enter("atxHeadingSequence"),sequenceOpen(t)}function sequenceOpen(t){return 35===t&&r++<6?(e.consume(t),sequenceOpen):null===t||markdownLineEndingOrSpace(t)?(e.exit("atxHeadingSequence"),atBreak(t)):n(t)}function atBreak(n){return 35===n?(e.enter("atxHeadingSequence"),sequenceFurther(n)):null===n||markdownLineEnding(n)?(e.exit("atxHeading"),t(n)):markdownSpace(n)?factorySpace(e,atBreak,"whitespace")(n):(e.enter("atxHeadingText"),data(n))}function sequenceFurther(t){return 35===t?(e.consume(t),sequenceFurther):(e.exit("atxHeadingSequence"),atBreak(t))}function data(t){return null===t||35===t||markdownLineEndingOrSpace(t)?(e.exit("atxHeadingText"),atBreak(t)):(e.consume(t),data)}}let em={name:"setextUnderline",resolveTo:resolveToSetextUnderline,tokenize:tokenizeSetextUnderline};function resolveToSetextUnderline(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let a={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",a,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=a,e.push(["exit",a,t]),e}function tokenizeSetextUnderline(e,t,n){let r;let i=this;return start;function start(t){let o,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){o="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||o)?(e.enter("setextHeadingLine"),r=t,before(t)):n(t)}function before(t){return e.enter("setextHeadingLineSequence"),inside(t)}function inside(t){return t===r?(e.consume(t),inside):(e.exit("setextHeadingLineSequence"),markdownSpace(t)?factorySpace(e,after,"lineSuffix")(t):after(t))}function after(r){return null===r||markdownLineEnding(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}let eg=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],ey=["pre","script","style","textarea"],ek={partial:!0,tokenize:tokenizeBlankLineBefore},ex={partial:!0,tokenize:tokenizeNonLazyContinuationStart};function resolveToHtmlFlow(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function tokenizeHtmlFlow(e,t,n){let r,i,o,a,l;let s=this;return start;function start(e){return before(e)}function before(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),open}function open(a){return 33===a?(e.consume(a),declarationOpen):47===a?(e.consume(a),i=!0,tagCloseStart):63===a?(e.consume(a),r=3,s.interrupt?t:continuationDeclarationInside):H(a)?(e.consume(a),o=String.fromCharCode(a),tagName):n(a)}function declarationOpen(i){return 45===i?(e.consume(i),r=2,commentOpenInside):91===i?(e.consume(i),r=5,a=0,cdataOpenInside):H(i)?(e.consume(i),r=4,s.interrupt?t:continuationDeclarationInside):n(i)}function commentOpenInside(r){return 45===r?(e.consume(r),s.interrupt?t:continuationDeclarationInside):n(r)}function cdataOpenInside(r){let i="CDATA[";return r===i.charCodeAt(a++)?(e.consume(r),a===i.length)?s.interrupt?t:continuation:cdataOpenInside:n(r)}function tagCloseStart(t){return H(t)?(e.consume(t),o=String.fromCharCode(t),tagName):n(t)}function tagName(a){if(null===a||47===a||62===a||markdownLineEndingOrSpace(a)){let l=47===a,c=o.toLowerCase();return!l&&!i&&ey.includes(c)?(r=1,s.interrupt?t(a):continuation(a)):eg.includes(o.toLowerCase())?(r=6,l)?(e.consume(a),basicSelfClosing):s.interrupt?t(a):continuation(a):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(a):i?completeClosingTagAfter(a):completeAttributeNameBefore(a))}return 45===a||j(a)?(e.consume(a),o+=String.fromCharCode(a),tagName):n(a)}function basicSelfClosing(r){return 62===r?(e.consume(r),s.interrupt?t:continuation):n(r)}function completeClosingTagAfter(t){return markdownSpace(t)?(e.consume(t),completeClosingTagAfter):completeEnd(t)}function completeAttributeNameBefore(t){return 47===t?(e.consume(t),completeEnd):58===t||95===t||H(t)?(e.consume(t),completeAttributeName):markdownSpace(t)?(e.consume(t),completeAttributeNameBefore):completeEnd(t)}function completeAttributeName(t){return 45===t||46===t||58===t||95===t||j(t)?(e.consume(t),completeAttributeName):completeAttributeNameAfter(t)}function completeAttributeNameAfter(t){return 61===t?(e.consume(t),completeAttributeValueBefore):markdownSpace(t)?(e.consume(t),completeAttributeNameAfter):completeAttributeNameBefore(t)}function completeAttributeValueBefore(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),l=t,completeAttributeValueQuoted):markdownSpace(t)?(e.consume(t),completeAttributeValueBefore):completeAttributeValueUnquoted(t)}function completeAttributeValueQuoted(t){return t===l?(e.consume(t),l=null,completeAttributeValueQuotedAfter):null===t||markdownLineEnding(t)?n(t):(e.consume(t),completeAttributeValueQuoted)}function completeAttributeValueUnquoted(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||markdownLineEndingOrSpace(t)?completeAttributeNameAfter(t):(e.consume(t),completeAttributeValueUnquoted)}function completeAttributeValueQuotedAfter(e){return 47===e||62===e||markdownSpace(e)?completeAttributeNameBefore(e):n(e)}function completeEnd(t){return 62===t?(e.consume(t),completeAfter):n(t)}function completeAfter(t){return null===t||markdownLineEnding(t)?continuation(t):markdownSpace(t)?(e.consume(t),completeAfter):n(t)}function continuation(t){return 45===t&&2===r?(e.consume(t),continuationCommentInside):60===t&&1===r?(e.consume(t),continuationRawTagOpen):62===t&&4===r?(e.consume(t),continuationClose):63===t&&3===r?(e.consume(t),continuationDeclarationInside):93===t&&5===r?(e.consume(t),continuationCdataInside):markdownLineEnding(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(ek,continuationAfter,continuationStart)(t)):null===t||markdownLineEnding(t)?(e.exit("htmlFlowData"),continuationStart(t)):(e.consume(t),continuation)}function continuationStart(t){return e.check(ex,continuationStartNonLazy,continuationAfter)(t)}function continuationStartNonLazy(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),continuationBefore}function continuationBefore(t){return null===t||markdownLineEnding(t)?continuationStart(t):(e.enter("htmlFlowData"),continuation(t))}function continuationCommentInside(t){return 45===t?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationRawTagOpen(t){return 47===t?(e.consume(t),o="",continuationRawEndTag):continuation(t)}function continuationRawEndTag(t){if(62===t){let n=o.toLowerCase();return ey.includes(n)?(e.consume(t),continuationClose):continuation(t)}return H(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),continuationRawEndTag):continuation(t)}function continuationCdataInside(t){return 93===t?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationDeclarationInside(t){return 62===t?(e.consume(t),continuationClose):45===t&&2===r?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationClose(t){return null===t||markdownLineEnding(t)?(e.exit("htmlFlowData"),continuationAfter(t)):(e.consume(t),continuationClose)}function continuationAfter(n){return e.exit("htmlFlow"),t(n)}}function tokenizeNonLazyContinuationStart(e,t,n){let r=this;return start;function start(t){return markdownLineEnding(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),after):n(t)}function after(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}function tokenizeBlankLineBefore(e,t,n){return start;function start(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(ee,t,n)}}let eb={partial:!0,tokenize:tokenizeNonLazyContinuation},ev={concrete:!0,name:"codeFenced",tokenize:tokenizeCodeFenced};function tokenizeCodeFenced(e,t,n){let r;let i=this,o={partial:!0,tokenize:tokenizeCloseStart},a=0,l=0;return start;function start(e){return beforeSequenceOpen(e)}function beforeSequenceOpen(t){let n=i.events[i.events.length-1];return a=n&&"linePrefix"===n[1].type?n[2].sliceSerialize(n[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),sequenceOpen(t)}function sequenceOpen(t){return t===r?(l++,e.consume(t),sequenceOpen):l<3?n(t):(e.exit("codeFencedFenceSequence"),markdownSpace(t)?factorySpace(e,infoBefore,"whitespace")(t):infoBefore(t))}function infoBefore(n){return null===n||markdownLineEnding(n)?(e.exit("codeFencedFence"),i.interrupt?t(n):e.check(eb,atNonLazyBreak,after)(n)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),info(n))}function info(t){return null===t||markdownLineEnding(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),infoBefore(t)):markdownSpace(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),factorySpace(e,metaBefore,"whitespace")(t)):96===t&&t===r?n(t):(e.consume(t),info)}function metaBefore(t){return null===t||markdownLineEnding(t)?infoBefore(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),meta(t))}function meta(t){return null===t||markdownLineEnding(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),infoBefore(t)):96===t&&t===r?n(t):(e.consume(t),meta)}function atNonLazyBreak(t){return e.attempt(o,after,contentBefore)(t)}function contentBefore(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),contentStart}function contentStart(t){return a>0&&markdownSpace(t)?factorySpace(e,beforeContentChunk,"linePrefix",a+1)(t):beforeContentChunk(t)}function beforeContentChunk(t){return null===t||markdownLineEnding(t)?e.check(eb,atNonLazyBreak,after)(t):(e.enter("codeFlowValue"),contentChunk(t))}function contentChunk(t){return null===t||markdownLineEnding(t)?(e.exit("codeFlowValue"),beforeContentChunk(t)):(e.consume(t),contentChunk)}function after(n){return e.exit("codeFenced"),t(n)}function tokenizeCloseStart(e,t,n){let o=0;return startBefore;function startBefore(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),start}function start(t){return e.enter("codeFencedFence"),markdownSpace(t)?factorySpace(e,beforeSequenceClose,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):beforeSequenceClose(t)}function beforeSequenceClose(t){return t===r?(e.enter("codeFencedFenceSequence"),sequenceClose(t)):n(t)}function sequenceClose(t){return t===r?(o++,e.consume(t),sequenceClose):o>=l?(e.exit("codeFencedFenceSequence"),markdownSpace(t)?factorySpace(e,sequenceCloseAfter,"whitespace")(t):sequenceCloseAfter(t)):n(t)}function sequenceCloseAfter(r){return null===r||markdownLineEnding(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}}function tokenizeNonLazyContinuation(e,t,n){let r=this;return start;function start(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),lineStart)}function lineStart(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}let ew={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},eS={}.hasOwnProperty;function decodeNamedCharacterReference(e){return!!eS.call(ew,e)&&ew[e]}let eE={name:"characterReference",tokenize:tokenizeCharacterReference};function tokenizeCharacterReference(e,t,n){let r,i;let o=this,a=0;return start;function start(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),open}function open(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),numeric):(e.enter("characterReferenceValue"),r=31,i=j,value(t))}function numeric(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=W,value):(e.enter("characterReferenceValue"),r=7,i=Q,value(t))}function value(l){if(59===l&&a){let r=e.exit("characterReferenceValue");return i!==j||decodeNamedCharacterReference(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(l),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(l)}return i(l)&&a++<r?(e.consume(l),value):n(l)}}let eC={name:"characterEscape",tokenize:tokenizeCharacterEscape};function tokenizeCharacterEscape(e,t,n){return start;function start(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),inside}function inside(r){return J(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}let eA={name:"lineEnding",tokenize:tokenizeLineEnding};function tokenizeLineEnding(e,t){return start;function start(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),factorySpace(e,t,"linePrefix")}}function resolveAll(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}let eL={name:"labelEnd",resolveAll:resolveAllLabelEnd,resolveTo:resolveToLabelEnd,tokenize:tokenizeLabelEnd},eI={tokenize:tokenizeResource},eT={tokenize:tokenizeReferenceFull},ez={tokenize:tokenizeReferenceCollapsed};function resolveAllLabelEnd(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&splice(e,0,e.length,n),e}function resolveToLabelEnd(e,t){let n,r,i,o,a=e.length,l=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){l=2;break}}else"labelEnd"===n.type&&(i=a);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},u={type:"labelText",start:{...e[r+l+2][1].end},end:{...e[i-2][1].start}};return o=push(o=[["enter",s,t],["enter",c,t]],e.slice(r+1,r+l+3)),o=push(o,[["enter",u,t]]),o=push(o,resolveAll(t.parser.constructs.insideSpan.null,e.slice(r+l+4,i-3),t)),o=push(o,[["exit",u,t],e[i-2],e[i-1],["exit",c,t]]),o=push(o,e.slice(i+1)),o=push(o,[["exit",s,t]]),splice(e,r,e.length,o),e}function tokenizeLabelEnd(e,t,n){let r,i;let o=this,a=o.events.length;for(;a--;)if(("labelImage"===o.events[a][1].type||"labelLink"===o.events[a][1].type)&&!o.events[a][1]._balanced){r=o.events[a][1];break}return start;function start(t){return r?r._inactive?labelEndNok(t):(i=o.parser.defined.includes(normalizeIdentifier(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),after):n(t)}function after(t){return 40===t?e.attempt(eI,labelEndOk,i?labelEndOk:labelEndNok)(t):91===t?e.attempt(eT,labelEndOk,i?referenceNotFull:labelEndNok)(t):i?labelEndOk(t):labelEndNok(t)}function referenceNotFull(t){return e.attempt(ez,labelEndOk,labelEndNok)(t)}function labelEndOk(e){return t(e)}function labelEndNok(e){return r._balanced=!0,n(e)}}function tokenizeResource(e,t,n){return resourceStart;function resourceStart(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),resourceBefore}function resourceBefore(t){return markdownLineEndingOrSpace(t)?factoryWhitespace(e,resourceOpen)(t):resourceOpen(t)}function resourceOpen(t){return 41===t?resourceEnd(t):factoryDestination(e,resourceDestinationAfter,resourceDestinationMissing,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function resourceDestinationAfter(t){return markdownLineEndingOrSpace(t)?factoryWhitespace(e,resourceBetween)(t):resourceEnd(t)}function resourceDestinationMissing(e){return n(e)}function resourceBetween(t){return 34===t||39===t||40===t?factoryTitle(e,resourceTitleAfter,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):resourceEnd(t)}function resourceTitleAfter(t){return markdownLineEndingOrSpace(t)?factoryWhitespace(e,resourceEnd)(t):resourceEnd(t)}function resourceEnd(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}function tokenizeReferenceFull(e,t,n){let r=this;return referenceFull;function referenceFull(t){return factoryLabel.call(r,e,referenceFullAfter,referenceFullMissing,"reference","referenceMarker","referenceString")(t)}function referenceFullAfter(e){return r.parser.defined.includes(normalizeIdentifier(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function referenceFullMissing(e){return n(e)}}function tokenizeReferenceCollapsed(e,t,n){return referenceCollapsedStart;function referenceCollapsedStart(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),referenceCollapsedOpen}function referenceCollapsedOpen(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}let eP={name:"labelStartImage",resolveAll:eL.resolveAll,tokenize:tokenizeLabelStartImage};function tokenizeLabelStartImage(e,t,n){let r=this;return start;function start(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),open}function open(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),after):n(t)}function after(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}function classifyCharacter(e){return null===e||markdownLineEndingOrSpace(e)||K(e)?1:Y(e)?2:void 0}let eD={name:"attention",resolveAll:resolveAllAttention,tokenize:tokenizeAttention};function resolveAllAttention(e,t){let n,r,i,o,a,l,s,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let p={...e[n][1].end},d={...e[u][1].start};movePoint(p,-l),movePoint(d,l),o={type:l>1?"strongSequence":"emphasisSequence",start:p,end:{...e[n][1].end}},a={type:l>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:d},i={type:l>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:l>1?"strong":"emphasis",start:{...o.start},end:{...a.end}},e[n][1].end={...o.start},e[u][1].start={...a.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=push(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=push(s,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),s=push(s,resolveAll(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),s=push(s,[["exit",i,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,s=push(s,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,splice(e,n-1,u-n+3,s),u=n+s.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e}function tokenizeAttention(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=classifyCharacter(i);return start;function start(t){return n=t,e.enter("attentionSequence"),inside(t)}function inside(a){if(a===n)return e.consume(a),inside;let l=e.exit("attentionSequence"),s=classifyCharacter(a),c=!s||2===s&&o||r.includes(a),u=!o||2===o&&s||r.includes(i);return l._open=!!(42===n?c:c&&(o||!u)),l._close=!!(42===n?u:u&&(s||!c)),t(a)}}function movePoint(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}function tokenizeAutolink(e,t,n){let r=0;return start;function start(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),open}function open(t){return H(t)?(e.consume(t),schemeOrEmailAtext):64===t?n(t):emailAtext(t)}function schemeOrEmailAtext(e){return 43===e||45===e||46===e||j(e)?(r=1,schemeInsideOrEmailAtext(e)):emailAtext(e)}function schemeInsideOrEmailAtext(t){return 58===t?(e.consume(t),r=0,urlInside):(43===t||45===t||46===t||j(t))&&r++<32?(e.consume(t),schemeInsideOrEmailAtext):(r=0,emailAtext(t))}function urlInside(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||asciiControl(r)?n(r):(e.consume(r),urlInside)}function emailAtext(t){return 64===t?(e.consume(t),emailAtSignOrDot):G(t)?(e.consume(t),emailAtext):n(t)}function emailAtSignOrDot(e){return j(e)?emailLabel(e):n(e)}function emailLabel(n){return 46===n?(e.consume(n),r=0,emailAtSignOrDot):62===n?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(n),e.exit("autolinkMarker"),e.exit("autolink"),t):emailValue(n)}function emailValue(t){if((45===t||j(t))&&r++<63){let n=45===t?emailValue:emailLabel;return e.consume(t),n}return n(t)}}function tokenizeHtmlText(e,t,n){let r,i,o;let a=this;return start;function start(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),open}function open(t){return 33===t?(e.consume(t),declarationOpen):47===t?(e.consume(t),tagCloseStart):63===t?(e.consume(t),instruction):H(t)?(e.consume(t),tagOpen):n(t)}function declarationOpen(t){return 45===t?(e.consume(t),commentOpenInside):91===t?(e.consume(t),i=0,cdataOpenInside):H(t)?(e.consume(t),declaration):n(t)}function commentOpenInside(t){return 45===t?(e.consume(t),commentEnd):n(t)}function comment(t){return null===t?n(t):45===t?(e.consume(t),commentClose):markdownLineEnding(t)?(o=comment,lineEndingBefore(t)):(e.consume(t),comment)}function commentClose(t){return 45===t?(e.consume(t),commentEnd):comment(t)}function commentEnd(e){return 62===e?end(e):45===e?commentClose(e):comment(e)}function cdataOpenInside(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?cdata:cdataOpenInside):n(t)}function cdata(t){return null===t?n(t):93===t?(e.consume(t),cdataClose):markdownLineEnding(t)?(o=cdata,lineEndingBefore(t)):(e.consume(t),cdata)}function cdataClose(t){return 93===t?(e.consume(t),cdataEnd):cdata(t)}function cdataEnd(t){return 62===t?end(t):93===t?(e.consume(t),cdataEnd):cdata(t)}function declaration(t){return null===t||62===t?end(t):markdownLineEnding(t)?(o=declaration,lineEndingBefore(t)):(e.consume(t),declaration)}function instruction(t){return null===t?n(t):63===t?(e.consume(t),instructionClose):markdownLineEnding(t)?(o=instruction,lineEndingBefore(t)):(e.consume(t),instruction)}function instructionClose(e){return 62===e?end(e):instruction(e)}function tagCloseStart(t){return H(t)?(e.consume(t),tagClose):n(t)}function tagClose(t){return 45===t||j(t)?(e.consume(t),tagClose):tagCloseBetween(t)}function tagCloseBetween(t){return markdownLineEnding(t)?(o=tagCloseBetween,lineEndingBefore(t)):markdownSpace(t)?(e.consume(t),tagCloseBetween):end(t)}function tagOpen(t){return 45===t||j(t)?(e.consume(t),tagOpen):47===t||62===t||markdownLineEndingOrSpace(t)?tagOpenBetween(t):n(t)}function tagOpenBetween(t){return 47===t?(e.consume(t),end):58===t||95===t||H(t)?(e.consume(t),tagOpenAttributeName):markdownLineEnding(t)?(o=tagOpenBetween,lineEndingBefore(t)):markdownSpace(t)?(e.consume(t),tagOpenBetween):end(t)}function tagOpenAttributeName(t){return 45===t||46===t||58===t||95===t||j(t)?(e.consume(t),tagOpenAttributeName):tagOpenAttributeNameAfter(t)}function tagOpenAttributeNameAfter(t){return 61===t?(e.consume(t),tagOpenAttributeValueBefore):markdownLineEnding(t)?(o=tagOpenAttributeNameAfter,lineEndingBefore(t)):markdownSpace(t)?(e.consume(t),tagOpenAttributeNameAfter):tagOpenBetween(t)}function tagOpenAttributeValueBefore(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,tagOpenAttributeValueQuoted):markdownLineEnding(t)?(o=tagOpenAttributeValueBefore,lineEndingBefore(t)):markdownSpace(t)?(e.consume(t),tagOpenAttributeValueBefore):(e.consume(t),tagOpenAttributeValueUnquoted)}function tagOpenAttributeValueQuoted(t){return t===r?(e.consume(t),r=void 0,tagOpenAttributeValueQuotedAfter):null===t?n(t):markdownLineEnding(t)?(o=tagOpenAttributeValueQuoted,lineEndingBefore(t)):(e.consume(t),tagOpenAttributeValueQuoted)}function tagOpenAttributeValueUnquoted(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||markdownLineEndingOrSpace(t)?tagOpenBetween(t):(e.consume(t),tagOpenAttributeValueUnquoted)}function tagOpenAttributeValueQuotedAfter(e){return 47===e||62===e||markdownLineEndingOrSpace(e)?tagOpenBetween(e):n(e)}function end(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function lineEndingBefore(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),lineEndingAfter}function lineEndingAfter(t){return markdownSpace(t)?factorySpace(e,lineEndingAfterPrefix,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):lineEndingAfterPrefix(t)}function lineEndingAfterPrefix(t){return e.enter("htmlTextData"),o(t)}}let eq={name:"labelStartLink",resolveAll:eL.resolveAll,tokenize:tokenizeLabelStartLink};function tokenizeLabelStartLink(e,t,n){let r=this;return start;function start(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),after}function after(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}function tokenizeHardBreakEscape(e,t,n){return start;function start(t){return e.enter("hardBreakEscape"),e.consume(t),after}function after(r){return markdownLineEnding(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}function resolveCodeText(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e}function previous(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type}function tokenizeCodeText(e,t,n){let r,i,o=0;return start;function start(t){return e.enter("codeText"),e.enter("codeTextSequence"),sequenceOpen(t)}function sequenceOpen(t){return 96===t?(e.consume(t),o++,sequenceOpen):(e.exit("codeTextSequence"),between(t))}function between(t){return null===t?n(t):32===t?(e.enter("space"),e.consume(t),e.exit("space"),between):96===t?(i=e.enter("codeTextSequence"),r=0,sequenceClose(t)):markdownLineEnding(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),between):(e.enter("codeTextData"),data(t))}function data(t){return null===t||32===t||96===t||markdownLineEnding(t)?(e.exit("codeTextData"),between(t)):(e.consume(t),data)}function sequenceClose(n){return 96===n?(e.consume(n),r++,sequenceClose):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(i.type="codeTextData",data(n))}}let eO={42:es,43:es,45:es,48:es,49:es,50:es,51:es,52:es,53:es,54:es,55:es,56:es,57:es,62:ep},eN={91:{name:"definition",tokenize:tokenizeDefinition}},eB={[-2]:ef,[-1]:ef,32:ef},eF={35:{name:"headingAtx",resolve:resolveHeadingAtx,tokenize:tokenizeHeadingAtx},42:el,45:[em,el],60:{concrete:!0,name:"htmlFlow",resolveTo:resolveToHtmlFlow,tokenize:tokenizeHtmlFlow},61:em,95:el,96:ev,126:ev},eR={38:eE,92:eC},e_={[-5]:eA,[-4]:eA,[-3]:eA,33:eP,38:eE,42:eD,60:[{name:"autolink",tokenize:tokenizeAutolink},{name:"htmlText",tokenize:tokenizeHtmlText}],91:eq,92:[{name:"hardBreakEscape",tokenize:tokenizeHardBreakEscape},eC],93:eL,95:eD,96:{name:"codeText",previous,resolve:resolveCodeText,tokenize:tokenizeCodeText}},eM={null:[eD,ei]},eV={null:[42,95]},eU={null:[]};function createTokenizer(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],a=[],l=[],s={attempt:constructFactory(onsuccessfulconstruct),check:constructFactory(onsuccessfulcheck),consume,enter,exit,interrupt:constructFactory(onsuccessfulcheck,{interrupt:!0})},c={code:null,containerState:{},defineSkip,events:[],now,parser:e,previous:null,sliceSerialize,sliceStream,write},u=t.tokenize.call(c,s);return t.resolveAll&&o.push(t),c;function write(e){return(a=push(a,e),main(),null!==a[a.length-1])?[]:(addResult(t,0),c.events=resolveAll(o,c.events,c),c.events)}function sliceSerialize(e,t){return serializeChunks(sliceStream(e),t)}function sliceStream(e){return sliceChunks(a,e)}function now(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function defineSkip(e){i[e.line]=e.column,accountForPotentialSkip()}function main(){let e;for(;r._index<a.length;){var t;let n=a[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;)t=n.charCodeAt(r._bufferIndex),u=u(t);else u=u(n)}}function consume(e){markdownLineEnding(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,accountForPotentialSkip()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e}function enter(e,t){let n=t||{};return n.type=e,n.start=now(),c.events.push(["enter",n,c]),l.push(n),n}function exit(e){let t=l.pop();return t.end=now(),c.events.push(["exit",t,c]),t}function onsuccessfulconstruct(e,t){addResult(e,t.from)}function onsuccessfulcheck(e,t){t.restore()}function constructFactory(e,t){return hook;function hook(n,r,i){let o,a,l,u;return Array.isArray(n)?handleListOfConstructs(n):"tokenize"in n?handleListOfConstructs([n]):handleMapOfConstructs(n);function handleMapOfConstructs(e){return start;function start(t){let n=null!==t&&e[t],r=null!==t&&e.null,i=[...Array.isArray(n)?n:n?[n]:[],...Array.isArray(r)?r:r?[r]:[]];return handleListOfConstructs(i)(t)}}function handleListOfConstructs(e){return(o=e,a=0,0===e.length)?i:handleConstruct(e[a])}function handleConstruct(e){return start;function start(n){return(u=store(),l=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?nok(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,s,ok,nok)(n)}}function ok(t){return e(l,u),r}function nok(e){return(u.restore(),++a<o.length)?handleConstruct(o[a]):i}}}function addResult(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&splice(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function store(){let e=now(),t=c.previous,n=c.currentConstruct,i=c.events.length,o=Array.from(l);return{from:i,restore};function restore(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,l=o,accountForPotentialSkip()}}function accountForPotentialSkip(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function sliceChunks(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,o=t.end._index,a=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,a)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}a>0&&n.push(e[o].slice(0,a))}return n}function serializeChunks(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o;let a=e[r];if("string"==typeof a)o=a;else switch(a){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(a)}n=-2===a,i.push(o)}return i.join("")}function parse_parse(e){let t=combineExtensions([i,...(e||{}).extensions||[]]),n={constructs:t,content:create(Z),defined:[],document:create(X),flow:create(er),lazy:{},string:create(eo),text:create(ea)};return n;function create(e){return creator;function creator(t){return createTokenizer(n,e,t)}}}let eH=/[\0\t\n\r]/g;function preprocess(){let e,t=1,n="",r=!0;return preprocessor;function preprocessor(i,o,a){let l,s,c,u,p;let d=[];for(i=n+("string"==typeof i?i.toString():new TextDecoder(o||void 0).decode(i)),c=0,n="",r&&(65279===i.charCodeAt(0)&&c++,r=void 0);c<i.length;){if(eH.lastIndex=c,u=(l=eH.exec(i))&&void 0!==l.index?l.index:i.length,p=i.charCodeAt(u),!l){n=i.slice(c);break}if(10===p&&c===u&&e)d.push(-3),e=void 0;else switch(e&&(d.push(-5),e=void 0),c<u&&(d.push(i.slice(c,u)),t+=u-c),p){case 0:d.push(65533),t++;break;case 9:for(s=4*Math.ceil(t/4),d.push(-2);t++<s;)d.push(-1);break;case 10:d.push(-4),t=1;break;default:e=!0,t=1}c=u+1}return a&&(e&&d.push(-5),n&&d.push(n),d.push(null)),d}}function decodeNumericCharacterReference(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let ej=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function decodeString(e){return e.replace(ej,decode)}function decode(e,t,n){if(t)return t;let r=n.charCodeAt(0);if(35===r){let e=n.charCodeAt(1),t=120===e||88===e;return decodeNumericCharacterReference(n.slice(t?2:1),t?16:10)}return decodeNamedCharacterReference(n)||e}let eG={}.hasOwnProperty;function fromMarkdown(e,t,n){return"string"!=typeof t&&(n=t,t=void 0),compiler(n)(postprocess(parse_parse(n).document().write(preprocess()(e,t,!0))))}function compiler(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:opener(link),autolinkProtocol:onenterdata,autolinkEmail:onenterdata,atxHeading:opener(heading),blockQuote:opener(blockQuote),characterEscape:onenterdata,characterReference:onenterdata,codeFenced:opener(codeFlow),codeFencedFenceInfo:buffer,codeFencedFenceMeta:buffer,codeIndented:opener(codeFlow,buffer),codeText:opener(codeText,buffer),codeTextData:onenterdata,data:onenterdata,codeFlowValue:onenterdata,definition:opener(definition),definitionDestinationString:buffer,definitionLabelString:buffer,definitionTitleString:buffer,emphasis:opener(emphasis),hardBreakEscape:opener(hardBreak),hardBreakTrailing:opener(hardBreak),htmlFlow:opener(html,buffer),htmlFlowData:onenterdata,htmlText:opener(html,buffer),htmlTextData:onenterdata,image:opener(image),label:buffer,link:opener(link),listItem:opener(listItem),listItemValue:onenterlistitemvalue,listOrdered:opener(list,onenterlistordered),listUnordered:opener(list),paragraph:opener(paragraph),reference:onenterreference,referenceString:buffer,resourceDestinationString:buffer,resourceTitleString:buffer,setextHeading:opener(heading),strong:opener(strong),thematicBreak:opener(thematicBreak)},exit:{atxHeading:closer(),atxHeadingSequence:onexitatxheadingsequence,autolink:closer(),autolinkEmail:onexitautolinkemail,autolinkProtocol:onexitautolinkprotocol,blockQuote:closer(),characterEscapeValue:onexitdata,characterReferenceMarkerHexadecimal:onexitcharacterreferencemarker,characterReferenceMarkerNumeric:onexitcharacterreferencemarker,characterReferenceValue:onexitcharacterreferencevalue,characterReference:onexitcharacterreference,codeFenced:closer(onexitcodefenced),codeFencedFence:onexitcodefencedfence,codeFencedFenceInfo:onexitcodefencedfenceinfo,codeFencedFenceMeta:onexitcodefencedfencemeta,codeFlowValue:onexitdata,codeIndented:closer(onexitcodeindented),codeText:closer(onexitcodetext),codeTextData:onexitdata,data:onexitdata,definition:closer(),definitionDestinationString:onexitdefinitiondestinationstring,definitionLabelString:onexitdefinitionlabelstring,definitionTitleString:onexitdefinitiontitlestring,emphasis:closer(),hardBreakEscape:closer(onexithardbreak),hardBreakTrailing:closer(onexithardbreak),htmlFlow:closer(onexithtmlflow),htmlFlowData:onexitdata,htmlText:closer(onexithtmltext),htmlTextData:onexitdata,image:closer(onexitimage),label:onexitlabel,labelText:onexitlabeltext,lineEnding:onexitlineending,link:closer(onexitlink),listItem:closer(),listOrdered:closer(),listUnordered:closer(),paragraph:closer(),referenceString:onexitreferencestring,resourceDestinationString:onexitresourcedestinationstring,resourceTitleString:onexitresourcetitlestring,resource:onexitresource,setextHeading:closer(onexitsetextheading),setextHeadingLineSequence:onexitsetextheadinglinesequence,setextHeadingText:onexitsetextheadingtext,strong:closer(),thematicBreak:closer()}};configure(t,(e||{}).mdastExtensions||[]);let n={};return compile;function compile(e){let r={type:"root",children:[]},i={stack:[r],tokenStack:[],config:t,enter,exit,buffer,resume,data:n},o=[],a=-1;for(;++a<e.length;)if("listOrdered"===e[a][1].type||"listUnordered"===e[a][1].type){if("enter"===e[a][0])o.push(a);else{let t=o.pop();a=prepareList(e,t,a)}}for(a=-1;++a<e.length;){let n=t[e[a][0]];eG.call(n,e[a][1].type)&&n[e[a][1].type].call(Object.assign({sliceSerialize:e[a][2].sliceSerialize},i),e[a][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1],t=e[1]||defaultOnError;t.call(i,void 0,e[0])}for(r.position={start:mdast_util_from_markdown_lib_point(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:mdast_util_from_markdown_lib_point(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},a=-1;++a<t.transforms.length;)r=t.transforms[a](r)||r;return r}function prepareList(e,t,n){let r,i,o,a,l=t-1,s=-1,c=!1;for(;++l<=n;){let t=e[l];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||a||s||o||(o=l),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let a=l;for(i=void 0;a--;){let t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",i=a}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||l,0,["exit",r,t[2]]),l++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(l,0,["enter",i,t[2]]),l++,n++,o=void 0,a=!0}}}return e[t][1]._spread=c,n}function opener(e,t){return open;function open(n){enter.call(this,e(n),n),t&&t.call(this,n)}}function buffer(){this.stack.push({type:"fragment",children:[]})}function enter(e,t,n){let r=this.stack[this.stack.length-1],i=r.children;i.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:mdast_util_from_markdown_lib_point(t.start),end:void 0}}function closer(e){return close;function close(t){e&&e.call(this,t),exit.call(this,t)}}function exit(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r){if(r[0].type!==e.type){if(t)t.call(this,e,r[0]);else{let t=r[1]||defaultOnError;t.call(this,e,r[0])}}}else throw Error("Cannot close `"+e.type+"` ("+stringifyPosition({start:e.start,end:e.end})+"): it’s not open");n.position.end=mdast_util_from_markdown_lib_point(e.end)}function resume(){return lib_toString(this.stack.pop())}function onenterlistordered(){this.data.expectingFirstListItemValue=!0}function onenterlistitemvalue(e){if(this.data.expectingFirstListItemValue){let t=this.stack[this.stack.length-2];t.start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}}function onexitcodefencedfenceinfo(){let e=this.resume(),t=this.stack[this.stack.length-1];t.lang=e}function onexitcodefencedfencemeta(){let e=this.resume(),t=this.stack[this.stack.length-1];t.meta=e}function onexitcodefencedfence(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)}function onexitcodefenced(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}function onexitcodeindented(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e.replace(/(\r?\n|\r)$/g,"")}function onexitdefinitionlabelstring(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=normalizeIdentifier(this.sliceSerialize(e)).toLowerCase()}function onexitdefinitiontitlestring(){let e=this.resume(),t=this.stack[this.stack.length-1];t.title=e}function onexitdefinitiondestinationstring(){let e=this.resume(),t=this.stack[this.stack.length-1];t.url=e}function onexitatxheadingsequence(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}}function onexitsetextheadingtext(){this.data.setextHeadingSlurpLineEnding=!0}function onexitsetextheadinglinesequence(e){let t=this.stack[this.stack.length-1];t.depth=61===this.sliceSerialize(e).codePointAt(0)?1:2}function onexitsetextheading(){this.data.setextHeadingSlurpLineEnding=void 0}function onenterdata(e){let t=this.stack[this.stack.length-1],n=t.children,r=n[n.length-1];r&&"text"===r.type||((r=text()).position={start:mdast_util_from_markdown_lib_point(e.start),end:void 0},n.push(r)),this.stack.push(r)}function onexitdata(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=mdast_util_from_markdown_lib_point(e.end)}function onexitlineending(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){let t=n.children[n.children.length-1];t.position.end=mdast_util_from_markdown_lib_point(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(onenterdata.call(this,e),onexitdata.call(this,e))}function onexithardbreak(){this.data.atHardBreak=!0}function onexithtmlflow(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}function onexithtmltext(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}function onexitcodetext(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}function onexitlink(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function onexitimage(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}function onexitlabeltext(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=decodeString(t),n.identifier=normalizeIdentifier(t).toLowerCase()}function onexitlabel(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t}function onexitresourcedestinationstring(){let e=this.resume(),t=this.stack[this.stack.length-1];t.url=e}function onexitresourcetitlestring(){let e=this.resume(),t=this.stack[this.stack.length-1];t.title=e}function onexitresource(){this.data.inReference=void 0}function onenterreference(){this.data.referenceType="collapsed"}function onexitreferencestring(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=normalizeIdentifier(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"}function onexitcharacterreferencemarker(e){this.data.characterReferenceType=e.type}function onexitcharacterreferencevalue(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;if(r)t=decodeNumericCharacterReference(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0;else{let e=decodeNamedCharacterReference(n);t=e}let i=this.stack[this.stack.length-1];i.value+=t}function onexitcharacterreference(e){let t=this.stack.pop();t.position.end=mdast_util_from_markdown_lib_point(e.end)}function onexitautolinkprotocol(e){onexitdata.call(this,e);let t=this.stack[this.stack.length-1];t.url=this.sliceSerialize(e)}function onexitautolinkemail(e){onexitdata.call(this,e);let t=this.stack[this.stack.length-1];t.url="mailto:"+this.sliceSerialize(e)}function blockQuote(){return{type:"blockquote",children:[]}}function codeFlow(){return{type:"code",lang:null,meta:null,value:""}}function codeText(){return{type:"inlineCode",value:""}}function definition(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function emphasis(){return{type:"emphasis",children:[]}}function heading(){return{type:"heading",depth:0,children:[]}}function hardBreak(){return{type:"break"}}function html(){return{type:"html",value:""}}function image(){return{type:"image",title:null,url:"",alt:null}}function link(){return{type:"link",title:null,url:"",children:[]}}function list(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}function listItem(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}function paragraph(){return{type:"paragraph",children:[]}}function strong(){return{type:"strong",children:[]}}function text(){return{type:"text",value:""}}function thematicBreak(){return{type:"thematicBreak"}}}function mdast_util_from_markdown_lib_point(e){return{line:e.line,column:e.column,offset:e.offset}}function configure(e,t){let n=-1;for(;++n<t.length;){let r=t[n];Array.isArray(r)?configure(e,r):extension(e,r)}}function extension(e,t){let n;for(n in t)if(eG.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}function defaultOnError(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+stringifyPosition({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+stringifyPosition({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+stringifyPosition({start:t.start,end:t.end})+") is still open")}function remarkParse(e){let t=this;function parser(n){return fromMarkdown(n,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}t.parser=parser}let eQ="object"==typeof self?self:globalThis,deserializer=(e,t)=>{let as=(t,n)=>(e.set(n,t),t),unpair=n=>{if(e.has(n))return e.get(n);let[r,i]=t[n];switch(r){case 0:case -1:return as(i,n);case 1:{let e=as([],n);for(let t of i)e.push(unpair(t));return e}case 2:{let e=as({},n);for(let[t,n]of i)e[unpair(t)]=unpair(n);return e}case 3:return as(new Date(i),n);case 4:{let{source:e,flags:t}=i;return as(new RegExp(e,t),n)}case 5:{let e=as(new Map,n);for(let[t,n]of i)e.set(unpair(t),unpair(n));return e}case 6:{let e=as(new Set,n);for(let t of i)e.add(unpair(t));return e}case 7:{let{name:e,message:t}=i;return as(new eQ[e](t),n)}case 8:return as(BigInt(i),n);case"BigInt":return as(Object(BigInt(i)),n);case"ArrayBuffer":return as(new Uint8Array(i).buffer,i);case"DataView":{let{buffer:e}=new Uint8Array(i);return as(new DataView(e),i)}}return as(new eQ[r](i),n)};return unpair},deserialize=e=>deserializer(new Map,e)(0),{toString:eW}={},{keys:eJ}=Object,typeOf=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=eW.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},shouldSkip=([e,t])=>0===e&&("function"===t||"symbol"===t),serializer=(e,t,n,r)=>{let as=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},pair=r=>{if(n.has(r))return n.get(r);let[i,o]=typeOf(r);switch(i){case 0:{let t=r;switch(o){case"bigint":i=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+o);t=null;break;case"undefined":return as([-1],r)}return as([i,t],r)}case 1:{if(o){let e=r;return"DataView"===o?e=new Uint8Array(r.buffer):"ArrayBuffer"===o&&(e=new Uint8Array(r)),as([o,[...e]],r)}let e=[],t=as([i,e],r);for(let t of r)e.push(pair(t));return t}case 2:{if(o)switch(o){case"BigInt":return as([o,r.toString()],r);case"Boolean":case"Number":case"String":return as([o,r.valueOf()],r)}if(t&&"toJSON"in r)return pair(r.toJSON());let n=[],a=as([i,n],r);for(let t of eJ(r))(e||!shouldSkip(typeOf(r[t])))&&n.push([pair(t),pair(r[t])]);return a}case 3:return as([i,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return as([i,{source:e,flags:t}],r)}case 5:{let t=[],n=as([i,t],r);for(let[n,i]of r)(e||!(shouldSkip(typeOf(n))||shouldSkip(typeOf(i))))&&t.push([pair(n),pair(i)]);return n}case 6:{let t=[],n=as([i,t],r);for(let n of r)(e||!shouldSkip(typeOf(n)))&&t.push(pair(n));return n}}let{message:a}=r;return as([i,{name:o,message:a}],r)};return pair},serialize=(e,{json:t,lossy:n}={})=>{let r=[];return serializer(!(t||n),!!t,new Map,r)(e),r},eY="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?deserialize(serialize(e,t)):structuredClone(e):(e,t)=>deserialize(serialize(e,t));function normalizeUri(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),a="";if(37===o&&j(e.charCodeAt(n+1))&&j(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(a=String.fromCharCode(o,t),i=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+i+1,a=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function defaultFootnoteBackContent(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function defaultFootnoteBackLabel(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function footer(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||defaultFootnoteBackContent,r=e.options.footnoteBackLabel||defaultFootnoteBackLabel,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},l=[],s=-1;for(;++s<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[s]);if(!i)continue;let o=e.all(i),a=String(i.identifier).toUpperCase(),c=normalizeUri(a.toLowerCase()),u=0,p=[],d=e.footnoteCounts.get(a);for(;void 0!==d&&++u<=d;){p.length>0&&p.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,u);"string"==typeof e&&(e={type:"text",value:e}),p.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=o[o.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...p)}else o.push(...p);let h={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(o,!0)};e.patch(i,h),l.push(h)}if(0!==l.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...eY(a),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(l,!0)},{type:"text",value:"\n"}]}}let convert=function(e){if(null==e)return lib_ok;if("function"==typeof e)return castFactory(e);if("object"==typeof e)return Array.isArray(e)?anyFactory(e):propsFactory(e);if("string"==typeof e)return typeFactory(e);throw Error("Expected function, string, or object as test")};function anyFactory(e){let t=[],n=-1;for(;++n<e.length;)t[n]=convert(e[n]);return castFactory(any);function any(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1}}function propsFactory(e){return castFactory(all);function all(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0}}function typeFactory(e){return castFactory(type);function type(t){return t&&t.type===e}}function castFactory(e){return check;function check(t,n,r){return!!(looksLikeANode(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function lib_ok(){return!0}function looksLikeANode(e){return null!==e&&"object"==typeof e&&"type"in e}function color(e){return"\x1b[33m"+e+"\x1b[39m"}let eK=[];function visitParents(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let o=convert(i),a=r?-1:1;function factory(e,i,l){let s=e&&"object"==typeof e?e:{};if("string"==typeof s.type){let t="string"==typeof s.tagName?s.tagName:"string"==typeof s.name?s.name:void 0;Object.defineProperty(visit,"name",{value:"node ("+color(e.type+(t?"<"+t+">":""))+")"})}return visit;function visit(){let s,c,u,p=eK;if((!t||o(e,i,l[l.length-1]||void 0))&&!1===(p=toResult(n(e,l)))[0])return p;if("children"in e&&e.children&&e.children&&"skip"!==p[0])for(c=(r?e.children.length:-1)+a,u=l.concat(e);c>-1&&c<e.children.length;){let t=e.children[c];if(!1===(s=factory(t,c,u)())[0])return s;c="number"==typeof s[1]?s[1]:c+a}return p}}factory(e,void 0,[])()}function toResult(e){return Array.isArray(e)?e:"number"==typeof e?[!0,e]:null==e?eK:[e]}function visit(e,t,n,r){let i,o,a;function overload(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)}"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,i=n):(o=t,a=n,i=r),visitParents(e,o,overload,i)}function blockquote(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function hardBreak(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]}function code(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i}function strikethrough(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function emphasis(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function footnoteReference(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=normalizeUri(i.toLowerCase()),a=e.footnoteOrder.indexOf(i),l=e.footnoteCounts.get(i);void 0===l?(l=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=a+1,l+=1,e.footnoteCounts.set(i,l);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)}function heading(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function html_html(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function revert(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&"text"===a.type?a.value+=r:i.push({type:"text",value:r}),i}function imageReference(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return revert(e,t);let i={src:normalizeUri(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)}function image_image(e,t){let n={src:normalizeUri(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function inlineCode(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function linkReference(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return revert(e,t);let i={href:normalizeUri(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)}function link_link(e,t){let n={href:normalizeUri(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function listItem(e,t,n){let r=e.all(t),i=n?listLoose(n):listItemLoose(t),o={},a=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let l=-1;for(;++l<r.length;){let e=r[l];(i||0!==l||"element"!==e.type||"p"!==e.tagName)&&a.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?a.push(e):a.push(...e.children)}let s=r[r.length-1];s&&(i||"element"!==s.type||"p"!==s.tagName)&&a.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:o,children:a};return e.patch(t,c),e.applyData(t,c)}function listLoose(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=listItemLoose(n[r])}return t}function listItemLoose(e){let t=e.spread;return null==t?e.children.length>1:t}function list_list(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)}function paragraph(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function root_root(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function strong(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function table(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=D(t.children[1]),a=P(t.children[t.children.length-1]);o&&a&&(r.position={start:o,end:a}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)}function tableRow(e,t,n){let r=n?n.children:void 0,i=r?r.indexOf(t):1,o=0===i?"th":"td",a=n&&"table"===n.type?n.align:void 0,l=a?a.length:t.children.length,s=-1,c=[];for(;++s<l;){let n=t.children[s],r={},i=a?a[s]:void 0;i&&(r.align=i);let l={type:"element",tagName:o,properties:r,children:[]};n&&(l.children=e.all(n),e.patch(n,l),l=e.applyData(n,l)),c.push(l)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(c,!0)};return e.patch(t,u),e.applyData(t,u)}function tableCell(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function trimLines(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(trimLine(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(trimLine(t.slice(i),i>0,!1)),o.join("")}function trimLine(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function handlers_text_text(e,t){let n={type:"text",value:trimLines(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function thematic_break_thematicBreak(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}let eZ={blockquote:blockquote,break:hardBreak,code:code,delete:strikethrough,emphasis:emphasis,footnoteReference:footnoteReference,heading:heading,html:html_html,imageReference:imageReference,image:image_image,inlineCode:inlineCode,linkReference:linkReference,link:link_link,listItem:listItem,list:list_list,paragraph:paragraph,root:root_root,strong:strong,table:table,tableCell:tableCell,tableRow:tableRow,text:handlers_text_text,thematicBreak:thematic_break_thematicBreak,toml:ignore,yaml:ignore,definition:ignore,footnoteDefinition:ignore};function ignore(){}let eX={}.hasOwnProperty,e$={};function createState(e,t){let n=t||e$,r=new Map,i=new Map,o=new Map,a={...eZ,...n.handlers},l={all,applyData,definitionById:r,footnoteById:i,footnoteCounts:o,footnoteOrder:[],handlers:a,one,options:n,patch,wrap};return visit(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),l;function one(e,t){let n=e.type,r=l.handlers[n];if(eX.call(l.handlers,n)&&r)return r(l,e,t);if(l.options.passThrough&&l.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=eY(n);return r.children=l.all(e),r}return eY(e)}let i=l.options.unknownHandler||defaultUnknownHandler;return i(l,e,t)}function all(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=l.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=trimMarkdownSpaceStart(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=trimMarkdownSpaceStart(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t}}function patch(e,t){e.position&&(t.position=position(e))}function applyData(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;if("string"==typeof t){if("element"===n.type)n.tagName=t;else{let e="children"in n?n.children:[n];n={type:"element",tagName:t,properties:{},children:e}}}"element"===n.type&&i&&Object.assign(n.properties,eY(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function defaultUnknownHandler(e,t){let n=t.data||{},r="value"in t&&!(eX.call(n,"hProperties")||eX.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function wrap(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function trimMarkdownSpaceStart(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function toHast(e,t){let n=createState(e,t),r=n.one(e,void 0),i=footer(n),o=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return i&&o.children.push({type:"text",value:"\n"},i),o}function remarkRehype(e,t){return e&&"run"in e?async function(n,r){let i=toHast(n,{file:r,...t});await e.run(i,r)}:function(n,r){return toHast(n,{file:r,...e||t})}}function bail(e){if(e)throw e}var e1=n(4635);function isPlainObject(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function trough(){let e=[],t={run,use};return t;function run(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);function next(i,...o){let a=e[++n],l=-1;if(i){r(i);return}for(;++l<t.length;)(null===o[l]||void 0===o[l])&&(o[l]=t[l]);t=o,a?lib_wrap(a,next)(...o):r(null,...o)}next(null,...t)}function use(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}}function lib_wrap(e,t){let n;return wrapped;function wrapped(...t){let r;let i=e.length>t.length;i&&t.push(done);try{r=e.apply(this,t)}catch(e){if(i&&n)throw e;return done(e)}i||(r&&r.then&&"function"==typeof r.then?r.then(then,done):r instanceof Error?done(r):then(r))}function done(e,...r){n||(n=!0,t(e,...r))}function then(e){done(null,e)}}var e0=n(9411),e2=n(7742);function isUrl(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}var e4=n(1041);let e3=["history","path","basename","stem","extname","dirname"];let VFile=class VFile{constructor(e){let t,n;t=e?isUrl(e)?{path:e}:"string"==typeof e||isUint8Array(e)?{value:e}:e:{},this.cwd="cwd"in t?"":e2.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<e3.length;){let e=e3[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)e3.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?e0.basename(this.path):void 0}set basename(e){assertNonEmpty(e,"basename"),assertPart(e,"basename"),this.path=e0.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?e0.dirname(this.path):void 0}set dirname(e){assertPath(this.basename,"dirname"),this.path=e0.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?e0.extname(this.path):void 0}set extname(e){if(assertPart(e,"extname"),assertPath(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=e0.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){isUrl(e)&&(e=(0,e4.fileURLToPath)(e)),assertNonEmpty(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?e0.basename(this.path,this.extname):void 0}set stem(e){assertNonEmpty(e,"stem"),assertPart(e,"stem"),this.path=e0.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new VFileMessage(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;let t=new TextDecoder(e||void 0);return t.decode(this.value)}};function assertPart(e,t){if(e&&e.includes(e0.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+e0.sep+"`")}function assertNonEmpty(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function assertPath(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}function isUint8Array(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}let CallableInstance=function(e){let t=this.constructor,n=t.prototype,r=n[e],apply=function(){return r.apply(apply,arguments)};return Object.setPrototypeOf(apply,n),apply},e6={}.hasOwnProperty;let Processor=class Processor extends CallableInstance{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=trough()}copy(){let e=new Processor,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(e1(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(assertUnfrozen("data",this.frozen),this.namespace[e]=t,this):e6.call(this.namespace,e)&&this.namespace[e]||void 0:e?(assertUnfrozen("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=vfile(e),n=this.parser||this.Parser;return assertParser("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),assertParser("process",this.parser||this.Parser),assertCompiler("process",this.compiler||this.Compiler),t?executor(void 0,t):new Promise(executor);function executor(r,i){let o=vfile(e),a=n.parse(o);function realDone(e,n){e||!n?i(e):r?r(n):t(void 0,n)}n.run(a,o,function(e,t,r){if(e||!t||!r)return realDone(e);let i=n.stringify(t,r);looksLikeAValue(i)?r.value=i:r.result=i,realDone(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),assertParser("processSync",this.parser||this.Parser),assertCompiler("processSync",this.compiler||this.Compiler),this.process(e,realDone),assertDone("processSync","process",n),t;function realDone(e,r){n=!0,bail(e),t=r}}run(e,t,n){assertNode(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?executor(void 0,n):new Promise(executor);function executor(i,o){let a=vfile(t);function realDone(t,r,a){let l=r||e;t?o(t):i?i(l):n(void 0,l,a)}r.run(e,a,realDone)}}runSync(e,t){let n,r=!1;return this.run(e,t,realDone),assertDone("runSync","run",r),n;function realDone(e,t){bail(e),n=t,r=!0}}stringify(e,t){this.freeze();let n=vfile(t),r=this.compiler||this.Compiler;return assertCompiler("stringify",r),assertNode(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(assertUnfrozen("use",this.frozen),null==e);else if("function"==typeof e)addPlugin(e,t);else if("object"==typeof e)Array.isArray(e)?addList(e):addPreset(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function add(e){if("function"==typeof e)addPlugin(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;addPlugin(t,n)}else addPreset(e)}else throw TypeError("Expected usable value, not `"+e+"`")}function addPreset(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");addList(e.plugins),e.settings&&(r.settings=e1(!0,r.settings,e.settings))}function addList(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){let n=e[t];add(n)}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function addPlugin(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,a=n[i][1];isPlainObject(a)&&isPlainObject(r)&&(r=e1(!0,a,r)),n[i]=[e,r,...o]}}}};let e5=new Processor().freeze();function assertParser(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function assertCompiler(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function assertUnfrozen(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function assertNode(e){if(!isPlainObject(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function assertDone(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function vfile(e){return looksLikeAVFile(e)?e:new VFile(e)}function looksLikeAVFile(e){return!!(e&&"object"==typeof e&&"message"in e&&"messages"in e)}function looksLikeAValue(e){return"string"==typeof e||lib_isUint8Array(e)}function lib_isUint8Array(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}let e9=[],e8={allowDangerousHtml:!0},e7=/^(https?|ircs?|mailto|xmpp)$/i,te=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Markdown(e){let t=createProcessor(e),n=createFile(e);return post(t.runSync(t.parse(n),n),e)}function createProcessor(e){let t=e.rehypePlugins||e9,n=e.remarkPlugins||e9,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...e8}:e8,i=e5().use(remarkParse).use(n).use(remarkRehype,r).use(t);return i}function createFile(e){let t=e.children||"",n=new VFile;return"string"==typeof t&&(n.value=t),n}function post(e,t){let n=t.allowedElements,r=t.allowElement,i=t.components,o=t.disallowedElements,a=t.skipHtml,l=t.unwrapDisallowed,s=t.urlTransform||defaultUrlTransform;for(let e of te)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return t.className&&(e={type:"element",tagName:"div",properties:{className:t.className},children:"root"===e.type?e.children:[e]}),visit(e,transform),toJsxRuntime(e,{Fragment:M.Fragment,components:i,ignoreInvalidStyle:!0,jsx:M.jsx,jsxs:M.jsxs,passKeys:!0,passNode:!0});function transform(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return a?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in _)if(Object.hasOwn(_,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=_[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=s(String(n||""),t,e))}}if("element"===e.type){let a=n?!n.includes(e.tagName):!!o&&o.includes(e.tagName);if(!a&&r&&"number"==typeof t&&(a=!r(e,t,i)),a&&i&&"number"==typeof t)return l&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}}function defaultUrlTransform(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||e7.test(e.slice(0,t))?e:""}}};