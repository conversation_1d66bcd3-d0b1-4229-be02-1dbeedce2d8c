"""
LLM Service for Resume Enhancement using Together.ai
"""

import os
import json
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    from together import Together
except ImportError:
    print("Warning: together package not installed. LLM features will be disabled.")
    Together = None

class LLMService:
    def __init__(self):
        self.api_key = os.getenv('TOGETHER_API_KEY')
        self.model = os.getenv('TOGETHER_MODEL', 'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo')
        self.fallback_model = os.getenv('TOGETHER_FALLBACK_MODEL', 'mistralai/Mixtral-8x7B-Instruct-v0.1')

        if Together and self.api_key and self.api_key != 'your_together_ai_api_key_here':
            self.client = Together(api_key=self.api_key)
            self.enabled = True
        else:
            self.enabled = False
            self.client = None
            print("LLM Service disabled: Missing API key or together package")
    
    def is_enabled(self) -> bool:
        """Check if LLM service is properly configured and enabled"""
        return self.enabled
    
    def _make_request(self, prompt: str, max_tokens: int = 1000, temperature: float = 0.7) -> Optional[str]:
        """Make a request to Together.ai API using new v1 format"""
        if not self.enabled or not self.client:
            return None

        try:
            # Convert prompt to messages format for chat completions
            messages = [{"role": "user", "content": prompt}]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                stop=["</s>", "[INST]", "[/INST]"]
            )

            if response and response.choices and len(response.choices) > 0:
                return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"Error with primary model {self.model}: {e}")
            # Try fallback model
            try:
                messages = [{"role": "user", "content": prompt}]
                response = self.client.chat.completions.create(
                    model=self.fallback_model,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.9,
                    stop=["</s>", "[INST]", "[/INST]"]
                )

                if response and response.choices and len(response.choices) > 0:
                    return response.choices[0].message.content.strip()

            except Exception as fallback_error:
                print(f"Error with fallback model {self.fallback_model}: {fallback_error}")

        return None
    
    def enhance_summary(self, original_summary: str, job_description: str, key_achievements: List[str] = None) -> str:
        """Enhance professional summary using LLM"""
        if not self.enabled:
            return original_summary
        
        achievements_text = ""
        if key_achievements:
            achievements_text = f"\nKey Achievements: {', '.join(key_achievements)}"
        
        prompt = f"""[INST] You are a professional resume writer. Rewrite this professional summary to perfectly align with the job description while maintaining authenticity.

Job Description: {job_description[:500]}...

Current Summary: {original_summary}
{achievements_text}

Requirements:
- Keep the core message and authenticity
- Use industry-specific terminology from the job description
- Highlight relevant skills and experience
- Create compelling narrative flow
- Optimize for ATS keywords naturally
- Maximum 3-4 sentences
- Professional tone

Enhanced Summary: [/INST]"""
        
        enhanced = self._make_request(prompt, max_tokens=300, temperature=0.6)
        return enhanced if enhanced else original_summary
    
    def enhance_bullet_points(self, bullet_points: List[str], job_description: str, job_title: str) -> List[str]:
        """Enhance work experience bullet points using LLM"""
        if not self.enabled or not bullet_points:
            return bullet_points
        
        bullets_text = "\n".join([f"• {bullet}" for bullet in bullet_points])
        
        prompt = f"""[INST] You are a professional resume writer. Enhance these work experience bullet points for maximum impact while maintaining truthfulness.

Target Role: {job_title}
Job Requirements: {job_description[:400]}...

Original Bullet Points:
{bullets_text}

Requirements:
- Use strong action verbs (Led, Developed, Implemented, Optimized, etc.)
- Quantify achievements where possible
- Align with job requirements and keywords
- Maintain truthfulness - don't add fake metrics
- Create compelling narrative
- Each bullet should be 1-2 lines maximum
- Start each with a strong action verb

Enhanced Bullet Points: [/INST]"""
        
        enhanced = self._make_request(prompt, max_tokens=500, temperature=0.7)
        
        if enhanced:
            # Parse the enhanced bullets back into a list
            enhanced_bullets = []
            for line in enhanced.split('\n'):
                line = line.strip()
                if line.startswith('•') or line.startswith('-'):
                    enhanced_bullets.append(line.lstrip('•- ').strip())
                elif line and not line.startswith(('Enhanced', 'Bullet', 'Points')):
                    enhanced_bullets.append(line)
            
            return enhanced_bullets if enhanced_bullets else bullet_points
        
        return bullet_points
    
    def generate_strategic_insights(self, resume_content: str, job_description: str) -> Dict[str, str]:
        """Generate strategic insights for resume positioning"""
        if not self.enabled:
            return {
                "alignment_score": "N/A - LLM service disabled",
                "key_strengths": "Please configure Together.ai API key",
                "recommendations": "LLM enhancement unavailable",
                "missing_keywords": "API key required"
            }
        
        prompt = f"""[INST] You are a career strategist. Analyze this candidate's background against the job requirements and provide strategic insights.

Job Description: {job_description[:600]}...

Candidate Background: {resume_content[:800]}...

Provide analysis in this exact format:

ALIGNMENT_SCORE: [Score 1-10 with brief explanation]

KEY_STRENGTHS: [Top 3 strengths that align with the role]

RECOMMENDATIONS: [3 specific recommendations for positioning]

MISSING_KEYWORDS: [Important keywords from job description that should be emphasized]

[/INST]"""
        
        response = self._make_request(prompt, max_tokens=400, temperature=0.6)
        
        if response:
            # Parse the structured response
            insights = {
                "alignment_score": "8/10",
                "key_strengths": "Strong technical background",
                "recommendations": "Emphasize leadership experience",
                "missing_keywords": "Review job description"
            }
            
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('ALIGNMENT_SCORE:'):
                    insights["alignment_score"] = line.replace('ALIGNMENT_SCORE:', '').strip()
                elif line.startswith('KEY_STRENGTHS:'):
                    insights["key_strengths"] = line.replace('KEY_STRENGTHS:', '').strip()
                elif line.startswith('RECOMMENDATIONS:'):
                    insights["recommendations"] = line.replace('RECOMMENDATIONS:', '').strip()
                elif line.startswith('MISSING_KEYWORDS:'):
                    insights["missing_keywords"] = line.replace('MISSING_KEYWORDS:', '').strip()
            
            return insights
        
        return {
            "alignment_score": "Unable to analyze",
            "key_strengths": "LLM service unavailable",
            "recommendations": "Please check API configuration",
            "missing_keywords": "Service error"
        }

# Global instance
llm_service = LLMService()
