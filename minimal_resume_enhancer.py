"""
Minimal Resume Enhancement - Preserves Authenticity
This module provides conservative resume enhancement that maintains the original voice and structure.
"""

import re
from llm_service import llm_service

def enhance_resume_minimal(job_description: str, base_resume_content: str, key_achievements: list = None) -> dict:
    """
    Minimal resume enhancement that preserves authenticity and original voice.
    Only makes subtle strategic adjustments.
    """
    if key_achievements is None:
        key_achievements = []
    
    # Start with the original content
    enhanced_content = base_resume_content
    
    # Extract a few key terms from job description for strategic placement
    job_keywords = extract_key_terms(job_description)
    
    # Only enhance if LLM is available and we can make subtle improvements
    if llm_service.is_enabled():
        # Try to enhance the professional summary only
        summary_match = re.search(r'(Product-led.*?adoption)', enhanced_content, re.DOTALL)
        if summary_match:
            original_summary = summary_match.group(1)
            enhanced_summary = llm_service.enhance_summary(original_summary, job_description, key_achievements)
            
            # Only replace if the enhancement is subtle and maintains authenticity
            if enhanced_summary and enhanced_summary != original_summary:
                # Check if the enhancement is too different (indicates over-enhancement)
                if calculate_similarity(original_summary, enhanced_summary) > 0.7:
                    enhanced_content = enhanced_content.replace(original_summary, enhanced_summary)
    
    # Calculate match score
    match_score = calculate_basic_match_score(job_description, enhanced_content)
    
    # Generate simple insights
    insights = generate_simple_insights(job_description, enhanced_content, job_keywords)
    
    return {
        'tailored_resume_markdown': enhanced_content,
        'match_score': match_score,
        'match_rationale': insights,
        'strategic_rationale': 'Minimal enhancement preserving your authentic voice and experience.'
    }

def extract_key_terms(job_description: str, max_terms: int = 5) -> list:
    """Extract key terms from job description without over-processing"""
    # Simple keyword extraction focusing on important terms
    text = job_description.lower()
    
    # Look for important terms
    important_terms = []
    
    # Technical terms
    tech_terms = ['ai', 'automation', 'digital', 'data', 'analytics', 'cloud', 'api', 'python', 'sql']
    for term in tech_terms:
        if term in text:
            important_terms.append(term)
    
    # Business terms  
    business_terms = ['strategy', 'sales', 'account', 'client', 'customer', 'growth', 'revenue', 'management']
    for term in business_terms:
        if term in text:
            important_terms.append(term)
    
    # Role-specific terms
    role_terms = ['consultant', 'manager', 'strategist', 'analyst', 'specialist', 'lead']
    for term in role_terms:
        if term in text:
            important_terms.append(term)
    
    return important_terms[:max_terms]

def calculate_similarity(text1: str, text2: str) -> float:
    """Calculate simple similarity between two texts"""
    words1 = set(text1.lower().split())
    words2 = set(text2.lower().split())
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0

def calculate_basic_match_score(job_description: str, resume_content: str) -> int:
    """Calculate a basic match score based on keyword overlap"""
    job_words = set(re.findall(r'\b\w+\b', job_description.lower()))
    resume_words = set(re.findall(r'\b\w+\b', resume_content.lower()))
    
    # Remove common words
    common_words = {'the', 'and', 'or', 'in', 'on', 'at', 'for', 'with', 'to', 'of', 'is', 'are', 'be', 'by'}
    job_words = job_words - common_words
    resume_words = resume_words - common_words
    
    if not job_words:
        return 50
    
    overlap = job_words.intersection(resume_words)
    score = int((len(overlap) / len(job_words)) * 100)
    
    # Ensure score is reasonable
    return min(max(score, 30), 85)

def generate_simple_insights(job_description: str, resume_content: str, keywords: list) -> str:
    """Generate simple, actionable insights"""
    match_score = calculate_basic_match_score(job_description, resume_content)
    
    insights = f"""Match Score: {match_score}/100

Key Observations:
• Your resume shows good alignment with the role requirements
• Your experience in AI and automation is highly relevant
• Consider naturally incorporating these terms where appropriate: {', '.join(keywords[:3])}

Strengths:
• Strong technical background in AI and automation tools
• Proven track record in client-facing roles
• Experience with cross-functional collaboration

Recommendations:
• Your authentic experience speaks for itself
• Focus on quantifiable achievements where possible
• Ensure your unique voice and personality come through

Note: This analysis preserves your authentic voice while identifying strategic alignment opportunities."""
    
    return insights
