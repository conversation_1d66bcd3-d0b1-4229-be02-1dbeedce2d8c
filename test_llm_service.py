#!/usr/bin/env python3
"""
Test script to verify Together.ai API integration
"""

import sys
import os
sys.path.append('.')

from llm_service import LLMService

def test_llm_service():
    print("🧪 Testing Together.ai LLM Service...")
    
    # Initialize service
    llm = LLMService()
    
    # Check if service is enabled
    print(f"✅ LLM Service Enabled: {llm.is_enabled()}")
    print(f"📋 API Key Present: {'Yes' if llm.api_key else 'No'}")
    print(f"🤖 Primary Model: {llm.model}")
    print(f"🔄 Fallback Model: {llm.fallback_model}")
    
    if not llm.is_enabled():
        print("❌ LLM Service is not enabled. Check your API key configuration.")
        return False
    
    # Test basic functionality
    print("\n🔍 Testing basic LLM functionality...")
    
    # Test summary enhancement
    test_summary = "Experienced professional with strong technical background."
    test_job_desc = "We are looking for a Senior Software Engineer with expertise in Python, AI, and cloud technologies."
    
    print("📝 Testing summary enhancement...")
    enhanced_summary = llm.enhance_summary(test_summary, test_job_desc)
    
    if enhanced_summary and enhanced_summary != test_summary:
        print("✅ Summary enhancement working!")
        print(f"Original: {test_summary}")
        print(f"Enhanced: {enhanced_summary[:100]}...")
    else:
        print("❌ Summary enhancement failed or returned same content")
        return False
    
    # Test bullet point enhancement
    print("\n📋 Testing bullet point enhancement...")
    test_bullets = ["Developed software applications", "Worked with team members"]
    enhanced_bullets = llm.enhance_bullet_points(test_bullets, test_job_desc, "Software Engineer")
    
    if enhanced_bullets and len(enhanced_bullets) >= len(test_bullets):
        print("✅ Bullet point enhancement working!")
        for i, bullet in enumerate(enhanced_bullets[:2]):
            print(f"  {i+1}. {bullet[:80]}...")
    else:
        print("❌ Bullet point enhancement failed")
        return False
    
    # Test strategic insights
    print("\n🎯 Testing strategic insights...")
    test_resume = "Software engineer with 5 years experience in Python and web development."
    insights = llm.generate_strategic_insights(test_resume, test_job_desc)
    
    if insights and isinstance(insights, dict):
        print("✅ Strategic insights working!")
        for key, value in insights.items():
            print(f"  {key}: {value[:60]}...")
    else:
        print("❌ Strategic insights failed")
        return False
    
    print("\n🎉 All LLM tests passed! Phase 1 is ready to go!")
    return True

if __name__ == "__main__":
    success = test_llm_service()
    sys.exit(0 if success else 1)
