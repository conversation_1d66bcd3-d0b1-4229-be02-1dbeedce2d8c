{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "bf2af48991abb54e2ec42525c75e6036", "previewModeSigningKey": "643353f84471b5d9d604691f3a339bcd2648411c7ac65e094faa6193cd6b418c", "previewModeEncryptionKey": "1700de0a4df4210403aeb60572246dbc85a77b737692d76c1f90615a5ff7f539"}}