<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Tailor - AI-Powered Resume Enhancement</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f7fafc;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #edf2f7;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #e6fffa;
        }
        
        textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }
        
        textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        input[type="file"] {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .success {
            background: #c6f6d5;
            color: #2d7d32;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .resume-output {
            background: white;
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            white-space: pre-wrap;
            font-family: 'Georgia', serif;
            line-height: 1.8;
        }
        
        .insights {
            background: #ebf8ff;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .match-score {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Resume Tailor</h1>
            <p>AI-Powered Resume Enhancement for Maximum Interview Success</p>
        </div>
        
        <div class="main-content">
            <form id="resumeForm">
                <!-- File Upload Section -->
                <div class="form-section">
                    <h3>📄 Upload Your Resume</h3>
                    <div class="upload-area" id="uploadArea">
                        <p>📁 Drag & drop your resume here or click to browse</p>
                        <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                            Supports PDF and text files (max 10MB)
                        </p>
                        <input type="file" id="fileInput" accept=".pdf,.txt,.doc,.docx">
                    </div>
                    <div id="uploadStatus"></div>
                </div>
                
                <!-- Job Description Section -->
                <div class="form-section">
                    <h3>💼 Job Description</h3>
                    <textarea 
                        id="jobDescription" 
                        placeholder="Paste the job description here... Include requirements, responsibilities, and company information for best results."
                        required
                    ></textarea>
                </div>
                
                <!-- Key Achievements Section -->
                <div class="form-section">
                    <h3>🏆 Key Achievements (Optional)</h3>
                    <textarea 
                        id="keyAchievements" 
                        placeholder="List 1-3 specific achievements or projects you want to highlight (one per line)..."
                        style="min-height: 100px;"
                    ></textarea>
                </div>
                
                <!-- Generate Button -->
                <button type="submit" class="btn" id="generateBtn">
                    ✨ Generate Tailored Resume
                </button>
            </form>
            
            <!-- Results Section -->
            <div id="results" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Global variables
        let resumeContent = '';
        
        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadStatus = document.getElementById('uploadStatus');
        const form = document.getElementById('resumeForm');
        const generateBtn = document.getElementById('generateBtn');
        const results = document.getElementById('results');
        
        // File upload handling
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);
        form.addEventListener('submit', handleFormSubmit);
        
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }
        
        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }
        
        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }
        
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        async function handleFile(file) {
            // Validate file
            if (file.size > 10 * 1024 * 1024) {
                showError('File size too large. Maximum size is 10MB.');
                return;
            }
            
            const allowedTypes = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
            if (!allowedTypes.includes(file.type)) {
                showError('Unsupported file type. Please upload a PDF or text file.');
                return;
            }
            
            // Upload file
            const formData = new FormData();
            formData.append('file', file);
            
            uploadStatus.innerHTML = '<div class="loading"><div class="spinner"></div>Uploading and processing file...</div>';
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resumeContent = result.text;
                    uploadStatus.innerHTML = `<div class="success">✅ File uploaded successfully! Extracted ${result.text.length} characters.</div>`;
                } else {
                    throw new Error(result.error || 'Upload failed');
                }
            } catch (error) {
                uploadStatus.innerHTML = `<div class="error">❌ Upload failed: ${error.message}</div>`;
            }
        }
        
        async function handleFormSubmit(e) {
            e.preventDefault();
            
            const jobDescription = document.getElementById('jobDescription').value.trim();
            const keyAchievements = document.getElementById('keyAchievements').value.trim();
            
            if (!jobDescription) {
                showError('Please enter a job description.');
                return;
            }
            
            if (!resumeContent) {
                showError('Please upload your resume first.');
                return;
            }
            
            // Show loading
            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 Generating...';
            results.style.display = 'block';
            results.innerHTML = '<div class="loading"><div class="spinner"></div>AI is analyzing your resume and tailoring it to the job description...<br><small>This may take 10-30 seconds</small></div>';
            
            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        job_description: jobDescription,
                        base_resume_content: resumeContent,
                        key_achievements: keyAchievements.split('\n').filter(a => a.trim())
                    })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    displayResults(result);
                } else {
                    throw new Error(result.error || 'Generation failed');
                }
            } catch (error) {
                results.innerHTML = `<div class="error">❌ Generation failed: ${error.message}</div>`;
            } finally {
                generateBtn.disabled = false;
                generateBtn.textContent = '✨ Generate Tailored Resume';
            }
        }
        
        function displayResults(result) {
            const matchScore = result.match_score || 'N/A';
            const tailoredResume = result.tailored_resume_markdown || result.tailored_resume || 'No resume generated';
            const rationale = result.match_rationale || 'No rationale provided';
            
            results.innerHTML = `
                <div class="results">
                    <h3>🎯 Results</h3>
                    <div class="match-score">Match Score: ${matchScore}/100</div>
                    
                    <div class="insights">
                        <h4>📊 Strategic Insights</h4>
                        <div style="white-space: pre-wrap; font-size: 0.9rem;">${rationale}</div>
                    </div>
                    
                    <h4 style="margin-top: 30px; margin-bottom: 15px;">📄 Your Tailored Resume</h4>
                    <div class="resume-output">${tailoredResume}</div>
                    
                    <button onclick="copyToClipboard()" class="btn" style="margin-top: 20px; margin-right: 10px;">
                        📋 Copy Resume to Clipboard
                    </button>
                    <button onclick="exportToPDF()" class="btn" style="margin-top: 20px; background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                        📄 Download PDF
                    </button>
                </div>
            `;
        }
        
        function copyToClipboard() {
            const resumeText = document.querySelector('.resume-output').textContent;
            navigator.clipboard.writeText(resumeText).then(() => {
                showSuccess('Resume copied to clipboard!');
            }).catch(() => {
                showError('Failed to copy to clipboard. Please select and copy manually.');
            });
        }

        async function exportToPDF() {
            const resumeContent = document.querySelector('.resume-output').textContent;

            if (!resumeContent) {
                showError('No resume content to export. Please generate a resume first.');
                return;
            }

            try {
                const response = await fetch('/export-pdf', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        resume_content: resumeContent
                    })
                });

                if (response.ok) {
                    // Create download link
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'resume_alex_guyenne.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showSuccess('PDF downloaded successfully!');
                } else {
                    const error = await response.json();
                    showError(`PDF export failed: ${error.error}`);
                }
            } catch (error) {
                showError(`PDF export failed: ${error.message}`);
            }
        }
        
        function showError(message) {
            uploadStatus.innerHTML = `<div class="error">❌ ${message}</div>`;
        }
        
        function showSuccess(message) {
            uploadStatus.innerHTML = `<div class="success">✅ ${message}</div>`;
            setTimeout(() => {
                uploadStatus.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
